{"dashboard": {"id": null, "title": "Airbyte Shopify Sync Dashboard", "tags": ["airbyte", "shopify", "sync"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "Webhook Requests Rate", "type": "stat", "targets": [{"expr": "rate(webhook_requests_total[5m])", "legendFormat": "{{shop_domain}} - {{topic}}"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Active Syncs", "type": "stat", "targets": [{"expr": "active_syncs", "legendFormat": "{{shop_domain}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 2}, {"color": "red", "value": 5}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Sync Job Success Rate", "type": "stat", "targets": [{"expr": "rate(sync_jobs_total{status=\"completed\"}[5m]) / rate(sync_jobs_total[5m]) * 100", "legendFormat": "Success Rate %"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "percent", "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Processing Lag", "type": "graph", "targets": [{"expr": "webhook_processing_lag_seconds", "legendFormat": "{{shop_domain}} - {{topic}}"}], "yAxes": [{"label": "Seconds", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Records Processed", "type": "graph", "targets": [{"expr": "rate(records_processed_total[5m])", "legendFormat": "{{shop_domain}} - {{entity}} - {{action}}"}], "yAxes": [{"label": "Records/sec", "min": 0}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}}, {"id": 6, "title": "Staging Records Pending", "type": "graph", "targets": [{"expr": "staging_records_pending", "legendFormat": "{{shop_domain}} - {{entity}}"}], "yAxes": [{"label": "Records", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 7, "title": "Error Rates", "type": "graph", "targets": [{"expr": "rate(webhook_hmac_failures_total[5m])", "legendFormat": "HMAC Failures - {{shop_domain}}"}, {"expr": "rate(processing_errors_total[5m])", "legendFormat": "Processing Errors - {{shop_domain}} - {{entity}}"}], "yAxes": [{"label": "Errors/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}], "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "templating": {"list": []}, "annotations": {"list": []}, "refresh": "5s", "schemaVersion": 27, "version": 0, "links": []}}