#!/bin/bash

# Airbyte Health Check Script
# This script tests the Airbyte setup to ensure everything is working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to test service health
test_service_health() {
    local service_name=$1
    local url=$2
    local expected_status=${3:-200}
    
    print_status "Testing $service_name at $url"
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_status"; then
        print_success "$service_name is healthy"
        return 0
    else
        print_error "$service_name is not responding correctly"
        return 1
    fi
}

# Function to test database connectivity
test_database() {
    local db_service=$1
    local db_name=$2
    local db_user=$3
    
    print_status "Testing database connectivity: $db_service"
    
    if docker-compose exec -T "$db_service" pg_isready -U "$db_user" -d "$db_name" > /dev/null 2>&1; then
        print_success "Database $db_service is ready"
        return 0
    else
        print_error "Database $db_service is not ready"
        return 1
    fi
}

# Function to check if services are running
check_services_running() {
    local services=("airbyte-db" "airbyte-temporal-db" "airbyte-temporal" "airbyte-server" "airbyte-worker" "airbyte-webapp")
    local all_running=true
    
    print_status "Checking if Airbyte services are running..."
    
    for service in "${services[@]}"; do
        if docker-compose ps | grep -q "$service.*Up"; then
            print_success "$service is running"
        else
            print_error "$service is not running"
            all_running=false
        fi
    done
    
    if [ "$all_running" = true ]; then
        return 0
    else
        return 1
    fi
}

# Main health check function
run_health_check() {
    local exit_code=0
    
    echo "========================================"
    echo "Airbyte Health Check"
    echo "========================================"
    echo ""
    
    # Check if services are running
    if ! check_services_running; then
        print_error "Some services are not running. Please start them first."
        exit_code=1
    fi
    
    echo ""
    print_status "Testing database connectivity..."
    
    # Test databases
    if ! test_database "airbyte-db" "airbyte" "airbyte"; then
        exit_code=1
    fi
    
    if ! test_database "airbyte-temporal-db" "temporal" "temporal"; then
        exit_code=1
    fi
    
    echo ""
    print_status "Testing service endpoints..."
    
    # Wait a bit for services to be fully ready
    sleep 5
    
    # Test Airbyte API
    if ! test_service_health "Airbyte API" "http://localhost:8001/api/v1/health" "200"; then
        exit_code=1
    fi
    
    # Test Airbyte Web UI
    if ! test_service_health "Airbyte Web UI" "http://localhost:8080" "200"; then
        exit_code=1
    fi
    
    echo ""
    
    if [ $exit_code -eq 0 ]; then
        print_success "All health checks passed!"
        echo ""
        echo "Airbyte is ready to use:"
        echo "  - Web UI: http://localhost:8080"
        echo "  - API: http://localhost:8001"
        echo "  - API Docs: http://localhost:8001/api/v1/openapi"
    else
        print_error "Some health checks failed. Please check the logs."
        echo ""
        echo "To view logs:"
        echo "  docker-compose logs airbyte-server"
        echo "  docker-compose logs airbyte-worker"
        echo "  docker-compose logs airbyte-temporal"
    fi
    
    return $exit_code
}

# Function to show API examples
show_api_examples() {
    echo "========================================"
    echo "Airbyte API Examples"
    echo "========================================"
    echo ""
    
    print_status "Testing API endpoints..."
    
    # Test health endpoint
    echo "1. Health Check:"
    echo "   curl http://localhost:8001/api/v1/health"
    curl -s http://localhost:8001/api/v1/health | jq . 2>/dev/null || curl -s http://localhost:8001/api/v1/health
    echo ""
    
    # Test workspaces endpoint
    echo "2. List Workspaces:"
    echo "   curl http://localhost:8001/api/v1/workspaces/list"
    curl -s -X POST http://localhost:8001/api/v1/workspaces/list -H "Content-Type: application/json" -d '{}' | jq . 2>/dev/null || curl -s -X POST http://localhost:8001/api/v1/workspaces/list -H "Content-Type: application/json" -d '{}'
    echo ""
    
    # Test source definitions
    echo "3. List Source Definitions (first 5):"
    echo "   curl http://localhost:8001/api/v1/source_definitions/list"
    curl -s -X POST http://localhost:8001/api/v1/source_definitions/list -H "Content-Type: application/json" -d '{}' | jq '.sourceDefinitions[:5] | .[] | {name: .name, dockerRepository: .dockerRepository}' 2>/dev/null || echo "API response received (install jq for formatted output)"
    echo ""
}

# Main script logic
case "${1:-health}" in
    health)
        run_health_check
        ;;
    api)
        show_api_examples
        ;;
    full)
        run_health_check
        if [ $? -eq 0 ]; then
            echo ""
            show_api_examples
        fi
        ;;
    help|--help|-h)
        echo "Airbyte Test Script"
        echo ""
        echo "Usage: $0 [COMMAND]"
        echo ""
        echo "Commands:"
        echo "  health    Run health checks (default)"
        echo "  api       Show API examples"
        echo "  full      Run health checks and show API examples"
        echo "  help      Show this help message"
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
