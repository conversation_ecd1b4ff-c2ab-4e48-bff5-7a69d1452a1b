#!/bin/bash

# Airbyte Management Script
# This script helps manage Airbyte services in your Docker Compose setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker Desktop and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to start Airbyte services
start_airbyte() {
    print_status "Starting Airbyte services..."
    
    # Start databases first
    print_status "Starting databases..."
    docker-compose up -d airbyte-db airbyte-temporal-db
    
    # Wait for databases to be healthy
    print_status "Waiting for databases to be ready..."
    docker-compose exec airbyte-db pg_isready -U airbyte -d airbyte || sleep 10
    docker-compose exec airbyte-temporal-db pg_isready -U temporal -d temporal || sleep 10
    
    # Start Temporal
    print_status "Starting Temporal service..."
    docker-compose up -d airbyte-temporal
    
    # Wait for Temporal to be ready
    print_status "Waiting for Temporal to be ready..."
    sleep 30
    
    # Start Airbyte bootloader
    print_status "Running Airbyte bootloader..."
    docker-compose up airbyte-bootloader
    
    # Start remaining Airbyte services
    print_status "Starting Airbyte services..."
    docker-compose up -d airbyte-server airbyte-worker airbyte-webapp airbyte-connector-builder-server
    
    print_success "Airbyte services started successfully!"
    print_status "Airbyte UI will be available at: http://localhost:8080"
    print_status "Airbyte API will be available at: http://localhost:8001"
}

# Function to stop Airbyte services
stop_airbyte() {
    print_status "Stopping Airbyte services..."
    docker-compose stop airbyte-webapp airbyte-connector-builder-server airbyte-worker airbyte-server airbyte-temporal airbyte-temporal-db airbyte-db
    print_success "Airbyte services stopped"
}

# Function to restart Airbyte services
restart_airbyte() {
    print_status "Restarting Airbyte services..."
    stop_airbyte
    sleep 5
    start_airbyte
}

# Function to check Airbyte service status
status_airbyte() {
    print_status "Checking Airbyte service status..."
    echo ""
    
    services=("airbyte-db" "airbyte-temporal-db" "airbyte-temporal" "airbyte-server" "airbyte-worker" "airbyte-webapp" "airbyte-connector-builder-server")
    
    for service in "${services[@]}"; do
        if docker-compose ps | grep -q "$service.*Up"; then
            print_success "$service: Running"
        else
            print_error "$service: Not running"
        fi
    done
    
    echo ""
    print_status "Service URLs:"
    echo "  - Airbyte UI: http://localhost:8080"
    echo "  - Airbyte API: http://localhost:8001"
}

# Function to view Airbyte logs
logs_airbyte() {
    service=${1:-""}
    if [ -z "$service" ]; then
        print_status "Showing logs for all Airbyte services..."
        docker-compose logs -f airbyte-server airbyte-worker airbyte-webapp airbyte-temporal
    else
        print_status "Showing logs for $service..."
        docker-compose logs -f "$service"
    fi
}

# Function to reset Airbyte (clean slate)
reset_airbyte() {
    print_warning "This will remove all Airbyte data and configurations!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Stopping and removing Airbyte services..."
        docker-compose down airbyte-webapp airbyte-connector-builder-server airbyte-worker airbyte-server airbyte-temporal airbyte-temporal-db airbyte-db
        
        print_status "Removing Airbyte volumes..."
        docker volume rm -f $(docker volume ls -q | grep airbyte) 2>/dev/null || true
        
        print_success "Airbyte reset complete. Run 'start' to initialize fresh."
    else
        print_status "Reset cancelled."
    fi
}

# Function to show help
show_help() {
    echo "Airbyte Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start Airbyte services"
    echo "  stop      Stop Airbyte services"
    echo "  restart   Restart Airbyte services"
    echo "  status    Check service status"
    echo "  logs      View logs (optionally specify service name)"
    echo "  reset     Reset Airbyte (removes all data)"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs airbyte-server"
    echo "  $0 status"
}

# Main script logic
case "${1:-help}" in
    start)
        check_docker
        start_airbyte
        ;;
    stop)
        check_docker
        stop_airbyte
        ;;
    restart)
        check_docker
        restart_airbyte
        ;;
    status)
        check_docker
        status_airbyte
        ;;
    logs)
        check_docker
        logs_airbyte "$2"
        ;;
    reset)
        check_docker
        reset_airbyte
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
