# Temporal Dynamic Configuration for Development
# This file configures Temporal for development use with Airbyte

# Frontend service configuration
frontend.enableUpdateWorkflowExecution:
  - value: true
    constraints: {}

# History service configuration  
history.enableCrossClusterOperations:
  - value: true
    constraints: {}

# Worker service configuration
worker.enableLoggingInReplay:
  - value: true
    constraints: {}

# System configuration
system.enableActivityLocalDispatchByDomain:
  - value: true
    constraints: {}

# Matching service configuration
matching.enableTaskInfoLogByDomainID:
  - value: true
    constraints: {}

# Visibility configuration
system.advancedVisibilityWritingMode:
  - value: "on"
    constraints: {}

# Development-specific settings
system.enableDebugMode:
  - value: true
    constraints: {}

# Workflow execution limits for development
limit.maxIDLength:
  - value: 1000
    constraints: {}

# Retention settings for development
system.defaultWorkflowRetentionTTL:
  - value: "72h"
    constraints: {}

# Enable cross-cluster operations for development
system.enableCrossClusterOperations:
  - value: true
    constraints: {}
