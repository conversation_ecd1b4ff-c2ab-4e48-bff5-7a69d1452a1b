# =============================================================================
# Airbyte Shopify Sync System - Environment Variables Template
# =============================================================================
# Copy this file to .env and fill in your actual values
# DO NOT commit .env files with real credentials to version control

# =============================================================================
# Database Configuration
# =============================================================================
DATABASE_URL=*******************************************************
POSTGRES_DB=ecommerce_db
POSTGRES_USER=app_user
POSTGRES_PASSWORD=dev_password

# =============================================================================
# Redis Configuration
# =============================================================================
REDIS_URL=redis://redis:6379/0

# =============================================================================
# Shopify Configuration
# =============================================================================
# Your Shopify app credentials
SHOPIFY_API_KEY=your_shopify_api_key_here
SHOPIFY_API_SECRET=your_shopify_api_secret_here

# Webhook secret for HMAC verification (generate a secure random string)
SHOPIFY_WEBHOOK_SECRET=your_webhook_secret_here

# Default shop domain for testing (optional)
SHOPIFY_SHOP_DOMAIN=your-shop.myshopify.com

# =============================================================================
# Airbyte Configuration
# =============================================================================
# Airbyte API credentials
AIRBYTE_USERNAME=airbyte
AIRBYTE_PASSWORD=password
AIRBYTE_API_URL=http://airbyte-server:8001/api/v1

# Airbyte workspace ID (will be created automatically)
AIRBYTE_WORKSPACE_ID=

# Airbyte API key (if using authentication)
AIRBYTE_API_KEY=

# Airbyte Environment Configuration
AIRBYTE_VERSION=0.63.15

# Database Configuration (shared across Airbyte services)
DATABASE_HOST=db
DATABASE_PORT=5432
DATABASE_PASSWORD=dev_password
DATABASE_USER=app_user
DATABASE_DB=airbyte_db
CONFIG_DATABASE_HOST=db
CONFIG_DATABASE_PORT=5432
CONFIG_DATABASE_PASSWORD=dev_password
CONFIG_DATABASE_USER=app_user
CONFIG_DATABASE_DB=airbyte_config_db

# Common Settings
LOG_LEVEL=INFO

# Airbyte Worker Configuration
AUTO_DETECT_SCHEMA=true
WORKSPACE_ROOT=/tmp/workspace
WORKSPACE_DOCKER_MOUNT=airbyte_workspace
LOCAL_ROOT=/tmp/airbyte_local
LOCAL_DOCKER_MOUNT=/tmp/airbyte_local
WEBAPP_URL=http://airbyte-webapp:80
TEMPORAL_HOST=airbyte-temporal:7233
WORKER_ENVIRONMENT=docker

# Storage Configuration
S3_LOG_BUCKET=
S3_LOG_BUCKET_REGION=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
S3_MINIO_ENDPOINT=http://minio:9000
S3_PATH_STYLE_ACCESS=true
GOOGLE_APPLICATION_CREDENTIALS=
GCS_LOG_BUCKET=

# Performance Settings
JOB_MAIN_CONTAINER_CPU_REQUEST=
JOB_MAIN_CONTAINER_CPU_LIMIT=
JOB_MAIN_CONTAINER_MEMORY_REQUEST=
JOB_MAIN_CONTAINER_MEMORY_LIMIT=
NORMALIZATION_JOB_MAIN_CONTAINER_MEMORY_LIMIT=
NORMALIZATION_JOB_MAIN_CONTAINER_MEMORY_REQUEST=
NORMALIZATION_JOB_MAIN_CONTAINER_CPU_LIMIT=
NORMALIZATION_JOB_MAIN_CONTAINER_CPU_REQUEST=

# Security & Features
SECRET_PERSISTENCE=TESTING_CONFIG_DB_TABLE
USE_STREAM_CAPABLE_STATE=true
MICRONAUT_ENVIRONMENTS=control-plane

# Airbyte Server Configuration
CONFIG_ROOT=/data
TRACKING_STRATEGY=logging

# Airbyte Webapp Configuration
API_URL=/api/v1/
PAPERCUPS_STOREFRONT_TOKEN=
FULLSTORY_ORG_ID=

# Temporal Configuration
DB=postgresql
POSTGRES_USER=app_user
POSTGRES_PWD=dev_password
POSTGRES_SEEDS=db
DYNAMIC_CONFIG_FILE_PATH=config/dynamicconfig/development.yaml

# =============================================================================
# Sync Configuration
# =============================================================================
# Maximum concurrent sync operations per shop
PER_SHOP_CONCURRENCY=1

# Maximum retry attempts for failed operations
SYNC_MAX_RETRIES=3

# Base delay for exponential backoff (seconds)
SYNC_BACKOFF_BASE=30

# Batch size for consumer processing
CONSUMER_BATCH_SIZE=100

# =============================================================================
# Celery Configuration
# =============================================================================
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# =============================================================================
# MinIO/S3 Configuration
# =============================================================================
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin123
MINIO_ENDPOINT=http://minio:9000

# =============================================================================
# Sync Service Configuration
# =============================================================================
# Maximum concurrent syncs per shop
MAX_CONCURRENT_SYNCS=3

# Rate limiting delay between API calls (seconds)
RATE_LIMIT_DELAY=2.0

# Batch size for consumer processing
BATCH_SIZE=100

# Checkpoint update interval (number of batches)
CHECKPOINT_INTERVAL=10

# =============================================================================
# Monitoring and Logging
# =============================================================================
LOG_LEVEL=INFO
PROMETHEUS_ENABLED=true

# =============================================================================
# Security Configuration
# =============================================================================
# JWT secret for API authentication
JWT_SECRET=your_jwt_secret_here

# Encryption key for sensitive data
ENCRYPTION_KEY=your_encryption_key_here

# =============================================================================
# Development/Testing Configuration
# =============================================================================
# Set to true for development mode
DEBUG=false
TESTING=false

# Mock external services for testing
MOCK_SHOPIFY_API=false
MOCK_AIRBYTE_API=false

# =============================================================================
# Production Configuration
# =============================================================================
# Set to production for production deployments
ENVIRONMENT=development

# External database URL for production
# DATABASE_URL=postgresql://user:password@host:port/database

# External Redis URL for production
# REDIS_URL=redis://host:port/db

# External S3 configuration for production
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# S3_BUCKET=your_s3_bucket
# S3_REGION=us-east-1
