# Airbyte Shopify Sync System

A production-ready, scalable system for syncing Shopify data using Airbyte OSS as the extraction/loader engine with comprehensive webhook handling, orchestration, and transactional data processing.

## 🏗️ Architecture Overview

```mermaid
graph TB
    subgraph "External"
        Shopify[Shopify Store]
    end

    subgraph "Webhook Layer"
        WHReceiver[Webhook Receiver Service]
        WHQueue[(Webhook Queue)]
    end

    subgraph "Airbyte OSS"
        AirbyteServer[Airbyte Server]
        AirbyteScheduler[Airbyte Scheduler]
        ShopifyConnector[Shopify Source Connector]
        PostgresConnector[Postgres Destination]
    end

    subgraph "Orchestration Layer"
        OrchWorker[Orchestration Worker]
        SyncQueue[(Sync Job Queue)]
    end

    subgraph "Data Layer"
        Postgres[(PostgreSQL)]
        StagingTables[Staging Tables]
        ProductionTables[Production Tables]
    end

    subgraph "Consumer Layer"
        Consumer[Consumer/Upsert Service]
        Checkpoints[Sync Checkpoints]
    end

    Shopify -->|Webhooks| WHReceiver
    WHReceiver --> WHQueue
    WHQueue --> OrchWorker
    OrchWorker -->|Trigger Sync| AirbyteServer
    AirbyteServer --> ShopifyConnector
    ShopifyConnector -->|GraphQL API| Shopify
    ShopifyConnector --> PostgresConnector
    PostgresConnector --> StagingTables
    Consumer --> StagingTables
    Consumer --> ProductionTables
    Consumer --> Checkpoints
```

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Python 3.11+
- PostgreSQL 15+
- Redis 7+

### 1. Clone and Setup

```bash
git clone <repository-url>
cd e-commerce
cp .env.example .env
# Edit .env with your configuration
```

### 2. Start Services

```bash
# Start all services
docker-compose up -d

# Check service health
docker-compose ps
```

### 3. Configure Airbyte Connection

```bash
# Wait for Airbyte to be ready
curl -f http://localhost:8080/health

# Set up Shopify connection
cd airbyte
python setup_connections.py \
  --shop-domain your-shop.myshopify.com \
  --access-token your-shopify-access-token \
  --trigger-sync
```

### 4. Configure Shopify Webhooks

In your Shopify admin, configure webhooks to point to:

```
https://your-domain.com/webhooks/shopify/your-shop.myshopify.com
```

## 📋 Services Overview

### Webhook Receiver Service

- **Port**: 8002
- **Purpose**: Receives and validates Shopify webhooks
- **Features**: HMAC validation, deduplication, immediate 200 OK responses
- **Health Check**: `http://localhost:8002/health`
- **Metrics**: `http://localhost:8002/metrics`

### Orchestration Worker Service

- **Port**: 8003
- **Purpose**: Processes webhook events and triggers Airbyte syncs
- **Features**: Rate limiting, concurrency control, exponential backoff
- **Health Check**: `http://localhost:8003/health`
- **Metrics**: `http://localhost:8003/metrics`

### Consumer/Upsert Service

- **Port**: 8004
- **Purpose**: Processes staging data and performs transactional upserts
- **Features**: Atomic commits, checkpoint management, conflict resolution
- **Health Check**: `http://localhost:8004/health`
- **Metrics**: `http://localhost:8004/metrics`

### Airbyte OSS

- **UI**: `http://localhost:8080`
- **API**: `http://localhost:8001/api/v1`
- **Purpose**: Data extraction and loading orchestration

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Database
DATABASE_URL=******************************************/ecommerce_db

# Shopify
SHOPIFY_WEBHOOK_SECRET=your-webhook-secret
SHOPIFY_API_KEY=your-api-key
SHOPIFY_API_SECRET=your-api-secret

# Airbyte
AIRBYTE_USERNAME=airbyte
AIRBYTE_PASSWORD=password

# Sync Configuration
MAX_CONCURRENT_SYNCS=3
RATE_LIMIT_DELAY=2.0
BATCH_SIZE=100
```

### Airbyte Configuration

The system uses configuration templates in `airbyte/configs/`:

- `shopify_source_template.json` - Shopify source configuration
- `postgres_destination_template.json` - PostgreSQL destination
- `connection_template.json` - Connection and stream settings

## 📊 Monitoring

### Prometheus Metrics

Access metrics at:

- Webhook Receiver: `http://localhost:8002/metrics`
- Orchestration Worker: `http://localhost:8003/metrics`
- Consumer Service: `http://localhost:8004/metrics`

### Grafana Dashboard

Import the dashboard from `monitoring/grafana-dashboard.json` to visualize:

- Webhook processing rates
- Sync job success rates
- Processing lag and performance
- Error rates and alerts

### Key Metrics

- `webhook_requests_total` - Total webhook requests by shop/topic/status
- `sync_jobs_total` - Total sync jobs by shop/entity/status
- `records_processed_total` - Records processed by shop/entity/action
- `active_syncs` - Currently active syncs per shop
- `staging_records_pending` - Unprocessed staging records

## 🧪 Testing

### Run Unit Tests

```bash
cd airbyte-sync
./run_tests.sh --unit-only
```

### Run Integration Tests

```bash
# Requires Docker services
./run_tests.sh --integration
```

### Test Coverage

```bash
pip install coverage
coverage run -m pytest
coverage report --show-missing
```

## 🔄 Data Flow

### 1. Webhook Processing

1. Shopify sends webhook to receiver service
2. HMAC signature validation
3. Event deduplication using SHA256 hash
4. Store in `webhook_events` table
5. Enqueue for processing

### 2. Sync Orchestration

1. Orchestration worker processes webhook queue
2. Determines if sync is needed based on topic
3. Checks concurrency limits per shop
4. Triggers Airbyte sync via API
5. Monitors sync job progress

### 3. Data Consumption

1. Airbyte loads data to staging tables
2. Consumer service processes staging data in batches
3. Transforms raw Shopify data to application schema
4. Performs transactional upserts with conflict resolution
5. Updates sync checkpoints

## 🛠️ Operations

### Scaling

#### Horizontal Scaling

- Run multiple instances of each service
- Use load balancer for webhook receiver
- Partition work by shop domain

#### Vertical Scaling

- Increase `BATCH_SIZE` for consumer service
- Adjust `MAX_CONCURRENT_SYNCS` per shop
- Tune database connection pools

### Backup and Recovery

#### Database Backups

```bash
# Backup
pg_dump -h localhost -U app_user ecommerce_db > backup.sql

# Restore
psql -h localhost -U app_user ecommerce_db < backup.sql
```

#### Checkpoint Recovery

The system automatically resumes from the last successful checkpoint after failures.

### Troubleshooting

#### Common Issues

1. **Webhook HMAC Failures**

   - Check `SHOPIFY_WEBHOOK_SECRET` configuration
   - Verify webhook endpoint URL

2. **Sync Jobs Stuck**

   - Check Airbyte service health
   - Review rate limiting configuration
   - Check shop concurrency limits

3. **High Processing Lag**
   - Scale consumer service instances
   - Increase `BATCH_SIZE`
   - Check database performance

#### Logs and Debugging

```bash
# View service logs
docker-compose logs -f webhook-receiver
docker-compose logs -f orchestration-worker
docker-compose logs -f consumer-service

# Check service health
curl http://localhost:8002/health
curl http://localhost:8003/health
curl http://localhost:8004/health
```

## 🔐 Security

### Webhook Security

- HMAC-SHA256 signature validation
- Request source IP logging
- Rate limiting protection

### API Security

- Environment-based secrets management
- No hardcoded credentials
- Secure inter-service communication

### Data Security

- Encrypted sensitive data storage
- Audit logging for data access
- Compliance with data protection regulations

## 📈 Performance

### Benchmarks

Typical performance characteristics:

- **Webhook Processing**: < 100ms response time
- **Sync Triggering**: < 5 seconds from webhook to sync start
- **Data Processing**: 1000+ records/minute per consumer instance

### Optimization Tips

1. **Database Optimization**

   - Use appropriate indexes
   - Regular VACUUM and ANALYZE
   - Connection pooling

2. **Redis Optimization**

   - Use Redis clustering for high availability
   - Monitor memory usage
   - Configure appropriate persistence

3. **Airbyte Optimization**
   - Tune connector batch sizes
   - Use incremental sync modes
   - Monitor resource usage

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔄 Migration from Existing System

See [MIGRATION.md](MIGRATION.md) for detailed instructions on migrating from your current Shopify sync system to this Airbyte-based solution.

## 🆘 Support

For support and questions:

- Create an issue in the repository
- Check the troubleshooting guide
- Review the monitoring dashboards
- Examine service logs for error details
