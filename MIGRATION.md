# Migration Guide: From Existing Shopify Sync to Airbyte System

This guide provides step-by-step instructions for migrating from your current Shopify sync system to the new Airbyte-based solution with minimal downtime.

## 📋 Pre-Migration Checklist

### 1. System Assessment

Before starting the migration, assess your current system:

- [ ] Document current sync frequency and data volumes
- [ ] Identify all Shopify entities being synced (products, orders, customers, etc.)
- [ ] Note any custom data transformations or business logic
- [ ] List all webhook endpoints and their purposes
- [ ] Document any downstream systems that depend on sync data

### 2. Environment Preparation

- [ ] Ensure Docker and Docker Compose are installed
- [ ] Verify PostgreSQL 15+ and Redis 7+ availability
- [ ] Confirm network connectivity to Shopify APIs
- [ ] Set up monitoring and alerting infrastructure
- [ ] Prepare backup and rollback procedures

### 3. Data Backup

**Critical**: Always backup your data before migration!

```bash
# Backup current database
pg_dump -h your-db-host -U your-user your-database > pre_migration_backup.sql

# Backup Redis data if applicable
redis-cli --rdb backup.rdb

# Export current sync state/checkpoints
# (Implementation depends on your current system)
```

## 🚀 Migration Process

### Phase 1: Parallel Setup (No Downtime)

#### Step 1: Deploy Airbyte Infrastructure

```bash
# Clone the new system
git clone <repository-url>
cd e-commerce

# Configure environment
cp .env.example .env
# Edit .env with your production settings

# Start Airbyte and supporting services
docker-compose up -d db redis airbyte-server airbyte-worker airbyte-webapp
```

#### Step 2: Configure Airbyte Connections

```bash
# Wait for Airbyte to be ready
curl -f http://localhost:8080/health

# Set up Shopify source and PostgreSQL destination
cd airbyte
python setup_connections.py \
  --shop-domain your-shop.myshopify.com \
  --access-token your-shopify-access-token \
  --start-date 2025-09-01  # Set appropriate start date
```

#### Step 3: Run Initial Backfill

```bash
# Trigger initial sync to populate staging tables
python setup_connections.py \
  --shop-domain your-shop.myshopify.com \
  --access-token your-shopify-access-token \
  --trigger-sync

# Monitor progress in Airbyte UI
open http://localhost:8080
```

#### Step 4: Deploy Sync Services

```bash
# Start webhook receiver, orchestration worker, and consumer service
docker-compose up -d webhook-receiver orchestration-worker consumer-service

# Verify services are healthy
curl http://localhost:8002/health  # Webhook receiver
curl http://localhost:8003/health  # Orchestration worker
curl http://localhost:8004/health  # Consumer service
```

### Phase 2: Data Validation (Parallel Running)

#### Step 5: Compare Data Integrity

Create validation scripts to compare data between old and new systems:

```python
# Example validation script
import asyncpg
import asyncio

async def validate_product_counts():
    # Connect to old system database
    old_conn = await asyncpg.connect("postgresql://old-system-db")
    
    # Connect to new system database  
    new_conn = await asyncpg.connect("postgresql://new-system-db")
    
    # Compare product counts
    old_count = await old_conn.fetchval("SELECT COUNT(*) FROM products")
    new_count = await new_conn.fetchval("SELECT COUNT(*) FROM products")
    
    print(f"Old system: {old_count} products")
    print(f"New system: {new_count} products")
    
    if abs(old_count - new_count) > 10:  # Allow small variance
        print("⚠️  Significant difference in product counts!")
    else:
        print("✅ Product counts match within tolerance")

# Run validation
asyncio.run(validate_product_counts())
```

#### Step 6: Test Webhook Processing

```bash
# Test webhook endpoint with sample data
curl -X POST http://localhost:8002/webhooks/shopify/your-shop.myshopify.com \
  -H "Content-Type: application/json" \
  -H "X-Shopify-Topic: products/update" \
  -H "X-Shopify-Hmac-Sha256: $(echo -n '{"test":"data"}' | openssl dgst -sha256 -hmac 'your-webhook-secret' -binary | base64)" \
  -d '{"test":"data"}'

# Verify webhook was processed
curl http://localhost:8002/metrics | grep webhook_requests_total
```

### Phase 3: Cutover (Minimal Downtime)

#### Step 7: Update Webhook Endpoints

**This step causes brief downtime for real-time updates**

1. **In Shopify Admin**, update webhook endpoints:
   - Old: `https://old-system.com/webhooks/shopify`
   - New: `https://new-system.com/webhooks/shopify/your-shop.myshopify.com`

2. **Update webhook topics** to include all required events:
   ```
   products/create
   products/update
   products/delete
   orders/create
   orders/updated
   orders/paid
   orders/cancelled
   orders/fulfilled
   customers/create
   customers/update
   customers/delete
   inventory_levels/update
   ```

#### Step 8: Stop Old Sync Jobs

```bash
# Stop old system sync jobs
# (Implementation depends on your current system)

# For Celery-based systems:
celery -A your_app control shutdown

# For cron-based systems:
crontab -r  # Remove cron jobs temporarily
```

#### Step 9: Final Data Sync

```bash
# Trigger final incremental sync to catch any missed updates
curl -X POST http://localhost:8003/api/trigger-sync \
  -H "Content-Type: application/json" \
  -d '{"shop_domain": "your-shop.myshopify.com", "entity": "all"}'

# Monitor sync completion
curl http://localhost:8003/status
```

### Phase 4: Verification and Cleanup

#### Step 10: Verify New System Operation

```bash
# Check all services are running
docker-compose ps

# Verify recent webhook processing
curl http://localhost:8002/metrics | grep webhook_requests_total

# Check sync job success rate
curl http://localhost:8003/metrics | grep sync_jobs_total

# Verify data processing
curl http://localhost:8004/metrics | grep records_processed_total
```

#### Step 11: Monitor for 24-48 Hours

- Monitor Grafana dashboards for anomalies
- Check error rates and processing lag
- Verify data consistency with spot checks
- Ensure downstream systems receive expected data

#### Step 12: Cleanup Old System (After Verification)

```bash
# Archive old system data
pg_dump old_system_db > old_system_archive.sql

# Decommission old services (only after confirming new system works)
# docker-compose -f old-system-compose.yml down
```

## 🔧 Migration Configuration

### Environment Variables for Migration

```bash
# Migration-specific settings
MIGRATION_MODE=true
MIGRATION_START_DATE=2025-09-01T00:00:00Z
MIGRATION_BATCH_SIZE=500
MIGRATION_PARALLEL_WORKERS=3

# Validation settings
ENABLE_DATA_VALIDATION=true
VALIDATION_SAMPLE_SIZE=1000
VALIDATION_TOLERANCE_PERCENT=1.0
```

### Airbyte Connection Settings for Migration

```json
{
  "start_date": "2025-09-01",
  "bulk_window_in_days": 30,
  "job_termination_threshold": 10000,
  "job_checkpoint_interval": 1000,
  "job_heartbeat_interval": 10800
}
```

## 🚨 Rollback Procedures

If issues arise during migration, follow these rollback steps:

### Immediate Rollback (< 1 hour after cutover)

```bash
# 1. Revert webhook endpoints in Shopify Admin
# 2. Restart old sync system
systemctl start old-sync-service

# 3. Restore old database if needed
psql -h old-db-host -U old-user old-database < pre_migration_backup.sql

# 4. Stop new system services
docker-compose down
```

### Delayed Rollback (> 1 hour after cutover)

```bash
# 1. Export new data since cutover
pg_dump new_system_db > post_cutover_data.sql

# 2. Merge new data with old system
# (Requires custom scripts based on your data model)

# 3. Follow immediate rollback steps
```

## 📊 Migration Monitoring

### Key Metrics to Monitor

1. **Data Consistency**
   - Record counts by entity type
   - Last updated timestamps
   - Data integrity checksums

2. **Performance**
   - Webhook processing latency
   - Sync job duration
   - Database query performance

3. **Error Rates**
   - Failed webhook deliveries
   - Sync job failures
   - Data validation errors

### Monitoring Queries

```sql
-- Check sync progress
SELECT 
    entity_name,
    last_updated_at,
    total_records,
    last_sync_status
FROM sync_checkpoints;

-- Monitor webhook processing
SELECT 
    topic,
    COUNT(*) as total,
    COUNT(*) FILTER (WHERE status = 'completed') as completed,
    COUNT(*) FILTER (WHERE status = 'failed') as failed
FROM webhook_events 
WHERE received_at > NOW() - INTERVAL '1 hour'
GROUP BY topic;

-- Check data freshness
SELECT 
    'products' as entity,
    MAX(updated_at) as latest_update,
    COUNT(*) as total_count
FROM products
UNION ALL
SELECT 
    'orders' as entity,
    MAX(updated_at) as latest_update,
    COUNT(*) as total_count
FROM orders;
```

## 🛠️ Troubleshooting Migration Issues

### Common Issues and Solutions

#### 1. Airbyte Connection Failures

**Symptoms**: Sync jobs fail to start or complete
**Solutions**:
- Verify Shopify API credentials
- Check network connectivity
- Review Airbyte connector logs
- Increase timeout settings

#### 2. Data Inconsistencies

**Symptoms**: Record counts don't match between systems
**Solutions**:
- Check sync start date configuration
- Verify entity filtering settings
- Review data transformation logic
- Run manual data reconciliation

#### 3. High Processing Lag

**Symptoms**: Webhook processing takes too long
**Solutions**:
- Scale consumer service instances
- Increase batch size settings
- Optimize database queries
- Add database indexes

#### 4. Memory/Resource Issues

**Symptoms**: Services crash or become unresponsive
**Solutions**:
- Increase Docker memory limits
- Optimize batch processing sizes
- Add resource monitoring
- Scale horizontally

### Emergency Contacts

During migration, ensure you have:
- Database administrator contact
- Shopify technical support
- Infrastructure team contact
- Business stakeholder notification list

## ✅ Post-Migration Checklist

After successful migration:

- [ ] All services running and healthy
- [ ] Webhook processing working correctly
- [ ] Data consistency validated
- [ ] Monitoring and alerting configured
- [ ] Documentation updated
- [ ] Team trained on new system
- [ ] Old system properly archived
- [ ] Performance benchmarks established
- [ ] Disaster recovery procedures tested

## 📞 Support During Migration

For migration support:
1. Review this guide thoroughly
2. Test in staging environment first
3. Have rollback procedures ready
4. Monitor system closely during cutover
5. Contact support team if issues arise

Remember: **Take your time and validate each step**. A successful migration is better than a fast one!
