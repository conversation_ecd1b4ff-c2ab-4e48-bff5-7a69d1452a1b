# Airbyte OSS Setup Documentation

## Overview

This document describes the modern Airbyte OSS setup integrated into your e-commerce platform. The setup follows the official Airbyte OSS best practices and includes all necessary components for production-ready data synchronization.

## Architecture

### Services Overview

The Airbyte setup includes the following services:

1. **airbyte-db**: Dedicated PostgreSQL database for Airbyte metadata
2. **airbyte-temporal-db**: Dedicated PostgreSQL database for Temporal workflows
3. **airbyte-temporal**: Temporal service for workflow orchestration
4. **airbyte-bootloader**: Initializes database schema (runs once)
5. **airbyte-server**: Main API and orchestration service
6. **airbyte-worker**: Executes sync jobs
7. **airbyte-webapp**: Web UI for managing connections
8. **airbyte-connector-builder-server**: For building custom connectors

### Key Improvements

- **Modern Version**: Using Airbyte 1.0.0 (latest stable)
- **Proper Temporal Integration**: Full workflow orchestration support
- **Isolated Databases**: Separate databases for Airbyte and Temporal
- **Production Ready**: Follows official best practices
- **Health Checks**: Proper dependency management and health monitoring

## Quick Start

### 1. Start All Services

```bash
# Start the entire stack
docker-compose up -d

# Or start just Airbyte services
docker-compose up -d airbyte-db airbyte-temporal-db airbyte-temporal airbyte-bootloader airbyte-server airbyte-worker airbyte-webapp airbyte-connector-builder-server
```

### 2. Access Airbyte

- **Web UI**: http://localhost:8080
- **API**: http://localhost:8001
- **API Documentation**: http://localhost:8001/api/v1/openapi

### 3. Initial Setup

1. Open the web UI at http://localhost:8080
2. Create your first workspace
3. Set up your first source (e.g., Shopify)
4. Configure your destination (your PostgreSQL database)
5. Create a connection to start syncing data

## Service Management

### Using Docker Compose

```bash
# Start Airbyte services
docker-compose up -d airbyte-db airbyte-temporal-db airbyte-temporal
docker-compose up airbyte-bootloader  # Run once to initialize
docker-compose up -d airbyte-server airbyte-worker airbyte-webapp

# Check service status
docker-compose ps | grep airbyte

# View logs
docker-compose logs -f airbyte-server
docker-compose logs -f airbyte-worker

# Stop services
docker-compose stop airbyte-webapp airbyte-worker airbyte-server airbyte-temporal airbyte-temporal-db airbyte-db
```

### Using the Management Script

```bash
# Make the script executable
chmod +x scripts/airbyte-management.sh

# Start Airbyte
./scripts/airbyte-management.sh start

# Check status
./scripts/airbyte-management.sh status

# View logs
./scripts/airbyte-management.sh logs

# Stop services
./scripts/airbyte-management.sh stop

# Reset everything (removes all data)
./scripts/airbyte-management.sh reset
```

## Configuration

### Environment Variables

Key configuration is in `airbyte.env`:

```bash
# Core settings
AIRBYTE_VERSION=1.0.0
DATABASE_HOST=airbyte-db
TEMPORAL_HOST=airbyte-temporal:7233

# Storage
WORKSPACE_ROOT=/tmp/workspace
CONFIG_ROOT=/data
STORAGE_TYPE=LOCAL

# Performance (adjust as needed)
JOB_MAIN_CONTAINER_CPU_REQUEST=
JOB_MAIN_CONTAINER_CPU_LIMIT=
JOB_MAIN_CONTAINER_MEMORY_REQUEST=
JOB_MAIN_CONTAINER_MEMORY_LIMIT=
```

### Database Configuration

- **Airbyte Database**: `airbyte-db` (user: airbyte, password: airbyte, database: airbyte)
- **Temporal Database**: `airbyte-temporal-db` (user: temporal, password: temporal, database: temporal)
- **Your App Database**: `db` (for destination connections)

## Integration with Your Application

### API Integration

Your application can interact with Airbyte via the REST API:

```python
import requests

# Airbyte API base URL
AIRBYTE_API_URL = "http://localhost:8001/api/v1"

# Example: List workspaces
response = requests.get(f"{AIRBYTE_API_URL}/workspaces")
workspaces = response.json()

# Example: Trigger a sync
sync_response = requests.post(
    f"{AIRBYTE_API_URL}/connections/{connection_id}/sync"
)
```

### Database Destinations

Configure your main PostgreSQL database as a destination:

```yaml
Destination Configuration:
  Host: db
  Port: 5432
  Database: ecommerce_db
  Username: app_user
  Password: dev_password
  Schema: raw  # Recommended for raw Airbyte data
```

## Troubleshooting

### Common Issues

1. **Services not starting**: Check Docker resources and logs
2. **Database connection errors**: Ensure databases are healthy
3. **Temporal issues**: Check temporal service logs and configuration
4. **Sync failures**: Check worker logs and connector configurations

### Debugging Commands

```bash
# Check service health
docker-compose ps
docker-compose logs airbyte-server
docker-compose logs airbyte-worker
docker-compose logs airbyte-temporal

# Check database connectivity
docker-compose exec airbyte-db pg_isready -U airbyte -d airbyte
docker-compose exec airbyte-temporal-db pg_isready -U temporal -d temporal

# Access database directly
docker-compose exec airbyte-db psql -U airbyte -d airbyte
docker-compose exec airbyte-temporal-db psql -U temporal -d temporal
```

### Performance Tuning

For production use, consider:

1. **Resource Limits**: Set appropriate CPU and memory limits
2. **Database Tuning**: Optimize PostgreSQL settings
3. **Storage**: Use persistent volumes for production
4. **Monitoring**: Set up monitoring and alerting

## Security Considerations

1. **Change Default Passwords**: Update database passwords in production
2. **Network Security**: Use proper network isolation
3. **API Security**: Implement authentication for API access
4. **Data Encryption**: Enable encryption for sensitive data

## Backup and Recovery

### Database Backups

```bash
# Backup Airbyte metadata
docker-compose exec airbyte-db pg_dump -U airbyte airbyte > airbyte_backup.sql

# Backup Temporal data
docker-compose exec airbyte-temporal-db pg_dump -U temporal temporal > temporal_backup.sql
```

### Volume Backups

```bash
# Backup workspace data
docker run --rm -v airbyte_workspace:/data -v $(pwd):/backup alpine tar czf /backup/workspace_backup.tar.gz /data

# Backup configuration data
docker run --rm -v airbyte_data:/data -v $(pwd):/backup alpine tar czf /backup/config_backup.tar.gz /data
```

## Migration from Old Setup

If you're migrating from the old Airbyte setup:

1. **Export Configurations**: Export your existing connections and sources
2. **Stop Old Services**: Stop the old Airbyte services
3. **Start New Services**: Start the new Airbyte setup
4. **Import Configurations**: Re-create your connections in the new setup
5. **Test Syncs**: Verify that syncs work correctly

## Next Steps

1. **Set up Shopify Source**: Configure your Shopify store as a source
2. **Configure Destinations**: Set up your PostgreSQL database as destination
3. **Create Connections**: Link sources to destinations
4. **Schedule Syncs**: Set up automatic sync schedules
5. **Monitor Performance**: Set up monitoring and alerting
6. **Scale as Needed**: Adjust resources based on usage

For more information, refer to the [official Airbyte documentation](https://docs.airbyte.com/).
