# ProductVideo Platform - Complete Architecture Documentation

## Overview

This document describes the comprehensive architecture of the ProductVideo platform, a SaaS solution that automatically generates professional product videos for e-commerce stores using AI. The platform integrates with Shopify for product synchronization, generates multi-modal media content (video/image/voice), and publishes content back to e-commerce platforms.

## System Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        A[Web Dashboard]
        B[API Clients]
        C[Shopify Webhooks]
    end

    subgraph "API Layer"
        D[FastAPI Server]
        E[Authentication & Authorization]
        F[Rate Limiting & Middleware]
    end

    subgraph "Processing Layer"
        G[Celery Workers]
        H[Task Queue - Redis]
        I[Background Processors]
    end

    subgraph "Data Layer"
        J[(PostgreSQL)]
        K[(Redis Cache)]
        L[Object Storage - S3/R2]
    end

    subgraph "External Services"
        M[Airbyte ETL]
        N[AI Providers]
        O[Shopify API]
    end

    A --> D
    B --> D
    C --> D
    D --> G
    D --> J
    G --> H
    G --> I
    I --> J
    I --> K
    I --> L
    I --> M
    I --> N
    I --> O
```

## Core Components

### API Layer Components

- **FastAPI Server** (`backend/src/servers/api/`): REST API endpoints with automatic OpenAPI docs
- **Authentication** (`backend/src/modules/auth/`): JWT-based auth with role-based access
- **Rate Limiting**: Built-in rate limiting and request throttling
- **Middleware**: Request logging, CORS, error handling

### Processing Layer Components

- **Celery Workers** (`backend/src/servers/worker/`): Distributed task processing
- **Task Queue**: Redis-based job queuing with priorities
- **Background Processors**:
  - `AirbyteSyncProcessor`: Processes Airbyte raw data → production tables
  - `MediaGenerationProcessor`: AI media generation (video/image/voice)
  - `MediaPushProcessor`: Platform media upload with retry logic
  - `AnalyticsProcessor`: Event processing and analytics aggregation

### Data Layer Components

- **PostgreSQL**: Primary database with async SQLAlchemy
- **Redis**: Caching, sessions, task queuing, distributed locks
- **Object Storage**: S3/CloudFlare R2 for media file storage
- **Airbyte**: ETL pipeline for Shopify data synchronization

### External Integrations

- **Shopify API**: OAuth, webhooks, GraphQL API for product/media management
- **AI Providers**: Google Veo 3 (video), Banana (images), Mock (testing)
- **Airbyte**: Open-source data integration platform
- **Monitoring**: Prometheus metrics, structured logging

## Complete Data Flow Architecture

```mermaid
graph TB
    subgraph "Entry Points"
        A1[Web Dashboard UI]
        A2[REST API Calls]
        A3[Shopify Webhooks]
    end

    subgraph "API Processing"
        B1[FastAPI Routes]
        B2[Authentication]
        B3[Input Validation]
        B4[Business Logic]
    end

    subgraph "Background Processing"
        C1[Celery Task Queue]
        C2[Worker Processes]
        C3[Retry Logic]
        C4[Error Handling]
    end

    subgraph "Data Processing"
        D1[Airbyte ETL]
        D2[Raw Tables]
        D3[Data Transformation]
        D4[Production Tables]
    end

    subgraph "AI Processing"
        E1[Media Generation]
        E2[Content Storage]
        E3[Platform Upload]
        E4[Analytics]
    end

    subgraph "External Services"
        F1[Shopify API]
        F2[AI Providers]
        F3[Object Storage]
        F4[Monitoring]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    B1 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> C4
    C4 --> D1
    D1 --> D2
    D2 --> D3
    D3 --> D4
    D4 --> E1
    E1 --> E2
    E2 --> E3
    E3 --> F1
    E4 --> F4
    C2 --> F2
    E2 --> F3
```

## Complete Flow Documentation

### 1. Store Onboarding & Setup Flow

**API Trigger**: `POST /api/stores` (Store creation endpoint)

```mermaid
graph TD
    A[POST /api/stores] --> B[Validate store data]
    B --> C[Create Store record in DB]
    C --> D[Setup Shopify OAuth]
    D --> E[Auto-create Airbyte infrastructure]
    E --> F[Create Shopify source connection]
    F --> G[Create PostgreSQL destination]
    G --> H[Setup sync catalog & connection]
    H --> I[Update store with Airbyte IDs]
    I --> J[Return store configuration]
    J --> K[Setup webhook subscriptions]
```

**Key Components:**
- `stores/router.py`: API endpoint for store creation
- `stores/service.py`: Business logic for store setup
- `airbyte_service.py`: Automated Airbyte resource creation
- Database models: Store, Airbyte connection metadata

### 2. Product Data Synchronization Flow

#### **Automatic Sync (Webhook-Driven)**

**API Trigger**: Shopify webhook → `POST /api/webhooks/shopify/products/*`

```mermaid
graph TD
    A[Shopify webhook received] --> B[Validate webhook signature]
    B --> C[Store webhook in WebhookEvent model]
    C --> D[Enqueue process_webhook_event task]
    D --> E[AirbyteSyncProcessor.process_business_logic]
    E --> F{Should trigger sync?}
    F -->|Yes| G[Enqueue trigger_airbyte_sync task]
    F -->|No| H[Skip sync - cooldown active]
    G --> I[Airbyte sync job started]
    I --> J[Monitor job completion]
    J --> K[Enqueue consumer_upsert task]
    K --> L[AirbyteSyncProcessor.process_airbyte_products]
    L --> M[Transform & upsert to production tables]
    M --> N[Update sync checkpoints]
    N --> O[Trigger business logic]
    O --> P[Video generation for new products]
```

#### **Manual Sync**

**API Trigger**: `POST /api/sync/{store_id}` (Manual sync endpoint)

```mermaid
graph TD
    A[POST /api/sync/{store_id}] --> B[Validate user permissions]
    B --> C[Check sync cooldown & locks]
    C --> D[Enqueue trigger_airbyte_sync task]
    D --> E[Airbyte sync job execution]
    E --> F[Monitor job progress]
    F --> G[Process raw data to production]
    G --> H[Return sync results & metrics]
```

### 2. Automatic Sync Flow (Webhook-Driven)

```mermaid
graph TD
    A[Shopify webhook received] --> B[Store webhook in WebhookEvent model]
    B --> C[Worker processes webhook]
    C --> D{Should trigger sync?}
    D -->|Yes| E[Trigger Airbyte sync job]
    D -->|No| F[Skip sync]
    E --> G[Monitor Airbyte job status]
    G --> H[Job completed?]
    H -->|No| G
    H -->|Yes| I[Process raw data to production]
    I --> J[Update sync status]
```

**Key Components:**

- `sync/models.py`: WebhookEvent, SyncJob models
- `worker/tasks.py`: process_webhook_event, trigger_airbyte_sync, monitor_airbyte_job
- `airbyte_service.py`: Sync triggering and status monitoring

### 3. AI Media Generation Flow

**API Trigger**: `POST /api/media/generate` (Media generation endpoint)

```mermaid
graph TD
    A[POST /api/media/generate] --> B[Validate user permissions]
    B --> C[Check product ownership & limits]
    C --> D[Create MediaJob in database]
    D --> E[Enqueue generate_media task]
    E --> F[MediaGenerationProcessor.process]
    F --> G[Select AI provider by media type]
    G --> H{Video Generation}
    H --> I[Veo3 API call with retry logic]
    H --> J{Image Generation}
    J --> K[Banana API call with retry logic]
    H --> L{Voice Generation}
    L --> M[TTS synthesis with retry logic]
    I --> N[Process & transcode video]
    K --> O[Upload image to storage]
    M --> P[Upload voice to storage]
    N --> Q[Update MediaVariant with URLs]
    O --> Q
    P --> Q
    Q --> R[Update job status to COMPLETED]
    R --> S[Return generation results]
```

**Key Components:**
- `media_generation/router.py`: API endpoints for media generation
- `MediaGenerationProcessor`: Handles AI provider selection and retry logic
- `video_transcoding_service.py`: Video processing and HLS generation
- `storage_service.py`: S3/CloudFlare R2 file uploads

### 4. Media Publishing Flow

**API Trigger**: `POST /api/media/push` (Media publishing endpoint)

```mermaid
graph TD
    A[POST /api/media/push] --> B[Validate variant ownership]
    B --> C[Check publishing permissions]
    C --> D[Enqueue push_to_shopify task]
    D --> E[MediaPushProcessor.process]
    E --> F[Get platform-specific service]
    F --> G{Shopify Publishing}
    G --> H[GraphQL productCreateMedia mutation]
    G --> I[Handle rate limiting & retries]
    G --> J[Update variant with media_id]
    J --> K[Update push status to COMPLETED]
    K --> L[Return publishing results]
    I --> M[Retry with exponential backoff]
    M --> G
```

**Key Components:**
- `media_generation/router.py`: Publishing API endpoints
- `MediaPushProcessor`: Platform-specific publishing with retry logic
- `shopify/media_service.py`: Shopify GraphQL API integration
- Rate limiting and error recovery mechanisms

### 5. Analytics Processing Flow

**API Trigger**: Internal events → `analytics/event_service.py`

```mermaid
graph TD
    A[User action triggers event] --> B[analytics_event_service.ingest_event]
    B --> C[Validate event data]
    C --> D[Enqueue process_analytics_event task]
    D --> E[AnalyticsProcessor.process]
    E --> F[Batch processing (up to 50 events)]
    F --> G[Event validation & transformation]
    G --> H[Database upsert with conflict resolution]
    H --> I[Update analytics aggregates]
    I --> J[Return processing results]
    J --> K[Prometheus metrics update]
```

**Key Components:**
- `analytics/event_service.py`: Event ingestion and validation
- `AnalyticsProcessor`: Batch processing with retry logic
- Database models for analytics data storage
- Prometheus metrics integration

### 4. Data Processing Flow

```mermaid
graph TD
    A[Raw data in Airbyte tables] --> B[consumer_upsert task]
    B --> C[AirbyteSyncProcessor.process_airbyte_*]
    C --> D[Extract and transform Shopify data]
    D --> E[Validate data integrity]
    E --> F[Upsert to production tables]
    F --> G[Update sync checkpoints]
    G --> H[Log processing metrics]
    H --> I[Trigger business logic]
    I --> J[Video generation for new products]
```

**Key Components:**

- `airbyte_sync_processor.py`: Processes raw Airbyte data into production tables
- `products/models.py`: Product and variant data models
- `sync/models.py`: SyncCheckpoint for progress tracking
- Business logic integration for video generation and analytics

## Component Interactions

### Airbyte Service Integration

- **Setup**: Called during store creation to establish ETL pipeline
- **Triggering**: Manual and automatic sync initiation
- **Monitoring**: Job status polling and completion detection
- **Configuration**: Uses JSON templates for source/destination setup

### Scalable Multi-Tenant Architecture

- **Shared Tables**: All stores use the same table names (e.g., `raw.products`, `raw.product_variants`)
- **Store Isolation**: `store_id` column in all tables for data separation
- **Compound Primary Keys**: `store_id` + `id` for proper deduplication
- **Benefits**: Reduced table count, better performance, easier maintenance

**Before (Not Scalable)**:

- Store 1: `shopify_1_products`, `shopify_1_product_variants`
- Store 2: `shopify_2_products`, `shopify_2_product_variants`
- **Problem**: 1000 stores × 4 tables = 4000+ tables

**After (Scalable)**:

- All stores: `raw.products`, `raw.product_variants` with `store_id` column
- **Benefit**: 4 shared tables total, regardless of store count

### Worker Server & Task Processing

#### **Celery Task Architecture**

```mermaid
graph TD
    subgraph "Task Producers"
        A1[API Endpoints]
        A2[Webhook Handlers]
        A3[Scheduled Tasks]
    end

    subgraph "Task Queue (Redis)"
        B1[High Priority Queue]
        B2[Default Priority Queue]
        B3[Low Priority Queue]
    end

    subgraph "Task Consumers"
        C1[Worker Pool 1]
        C2[Worker Pool 2]
        C3[Worker Pool N]
    end

    subgraph "Task Processors"
        D1[AirbyteSyncProcessor]
        D2[MediaGenerationProcessor]
        D3[MediaPushProcessor]
        D4[AnalyticsProcessor]
    end

    A1 --> B1
    A2 --> B2
    A3 --> B3
    B1 --> C1
    B2 --> C2
    B3 --> C3
    C1 --> D1
    C2 --> D2
    C3 --> D3
    C1 --> D4
```

#### **Task Definitions**

```python
# Sync Tasks
process_webhook_event     # Webhook processing pipeline
trigger_airbyte_sync      # Airbyte job orchestration
consumer_upsert           # Raw data processing
monitor_airbyte_job       # Job status monitoring

# Media Tasks
generate_media           # AI content generation
push_to_shopify          # Platform publishing
process_analytics_event  # Analytics processing

# Utility Tasks
cleanup_old_data         # Data maintenance
health_check            # System monitoring
```

#### **Processor Responsibilities**

- **AirbyteSyncProcessor**:
  - Processes raw Airbyte data into production tables
  - Handles Shopify product/variant transformations
  - Manages sync checkpoints and deduplication
  - Triggers business logic for new products

- **MediaGenerationProcessor**:
  - Orchestrates AI provider selection (Veo3/Banana/TTS)
  - Handles retry logic and error recovery
  - Manages video transcoding and storage uploads
  - Updates generation job status and metrics

- **MediaPushProcessor**:
  - Manages platform-specific publishing (Shopify)
  - Handles rate limiting and API quotas
  - Provides retry logic for failed uploads
  - Updates publishing status and audit trails

- **AnalyticsProcessor**:
  - Processes user interaction events
  - Handles batch processing for performance
  - Manages analytics data aggregation
  - Provides real-time metrics and insights

### Database Models & Schema

#### **Core Business Models**

```sql
-- User Management
users (
    id UUID PRIMARY KEY,
    email VARCHAR UNIQUE,
    hashed_password VARCHAR,
    is_active BOOLEAN,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

-- Multi-tenant Stores
stores (
    id UUID PRIMARY KEY,
    owner_id UUID REFERENCES users(id),
    shop_domain VARCHAR UNIQUE,
    admin_access_token VARCHAR, -- Encrypted
    platform VARCHAR, -- shopify, woocommerce, etc.
    is_active BOOLEAN,
    -- Airbyte integration fields
    airbyte_source_id VARCHAR,
    airbyte_destination_id VARCHAR,
    airbyte_connection_id VARCHAR,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

-- Product Catalog
products (
    id VARCHAR PRIMARY KEY, -- Shopify product ID
    store_id UUID REFERENCES stores(id),
    title VARCHAR,
    description TEXT,
    handle VARCHAR,
    product_type VARCHAR,
    vendor VARCHAR,
    status VARCHAR,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    synced_at TIMESTAMP
)

-- Product Variants
product_variants (
    id VARCHAR PRIMARY KEY, -- Shopify variant ID
    store_id UUID REFERENCES stores(id),
    product_id VARCHAR REFERENCES products(id),
    title VARCHAR,
    sku VARCHAR,
    price DECIMAL,
    compare_at_price DECIMAL,
    inventory_quantity INTEGER,
    weight DECIMAL,
    weight_unit VARCHAR,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### **Media Generation Models**

```sql
-- Media Generation Jobs
media_jobs (
    id SERIAL PRIMARY KEY,
    tenant_id UUID REFERENCES users(id),
    product_id VARCHAR,
    status VARCHAR, -- pending, processing, completed, failed
    media_type VARCHAR, -- video, image, voice
    script TEXT,
    custom_config JSONB,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    error_message TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

-- Generated Media Variants
media_variants (
    id SERIAL PRIMARY KEY,
    job_id INTEGER REFERENCES media_jobs(id),
    tenant_id UUID REFERENCES users(id),
    product_id VARCHAR,
    variant_name VARCHAR,
    status VARCHAR, -- generating, ready, failed
    -- Media URLs
    video_url VARCHAR,
    image_url VARCHAR,
    voice_url VARCHAR,
    thumbnail_url VARCHAR,
    -- Processing metadata
    provider_media_id VARCHAR,
    generation_params JSONB,
    -- Publishing status
    push_status VARCHAR, -- pending, pushing, completed, failed
    media_id VARCHAR, -- Platform media ID
    pushed_at TIMESTAMP,
    push_error_message TEXT,
    -- User interaction
    is_favorite BOOLEAN DEFAULT FALSE,
    user_rating INTEGER,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

#### **Sync & Processing Models**

```sql
-- Webhook Events
webhook_events (
    id SERIAL PRIMARY KEY,
    store_id UUID REFERENCES stores(id),
    topic VARCHAR,
    payload JSONB,
    processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP,
    created_at TIMESTAMP
)

-- Airbyte Sync Jobs
sync_jobs (
    id SERIAL PRIMARY KEY,
    store_id UUID REFERENCES stores(id),
    airbyte_job_id VARCHAR,
    status VARCHAR, -- running, succeeded, failed
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    records_synced INTEGER,
    error_message TEXT,
    created_at TIMESTAMP
)

-- Sync Checkpoints
sync_checkpoints (
    id SERIAL PRIMARY KEY,
    store_id UUID REFERENCES stores(id),
    entity_type VARCHAR, -- products, variants, orders
    last_synced_id VARCHAR,
    last_synced_at TIMESTAMP,
    updated_at TIMESTAMP,
    UNIQUE(store_id, entity_type)
)

-- Raw Airbyte Staging Tables
raw_products (
    _airbyte_ab_id VARCHAR PRIMARY KEY,
    _airbyte_emitted_at TIMESTAMP,
    _airbyte_normalized_at TIMESTAMP,
    store_id UUID,
    id VARCHAR,
    title VARCHAR,
    -- ... other Shopify fields
)

raw_product_variants (
    _airbyte_ab_id VARCHAR PRIMARY KEY,
    _airbyte_emitted_at TIMESTAMP,
    _airbyte_normalized_at TIMESTAMP,
    store_id UUID,
    id VARCHAR,
    product_id VARCHAR,
    -- ... other Shopify fields
)
```

#### **Analytics Models**

```sql
-- Analytics Events
analytics_events (
    id SERIAL PRIMARY KEY,
    tenant_id UUID REFERENCES users(id),
    event_type VARCHAR,
    event_data JSONB,
    user_id UUID,
    session_id VARCHAR,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP
)

-- Analytics Aggregates
analytics_aggregates (
    id SERIAL PRIMARY KEY,
    tenant_id UUID REFERENCES users(id),
    metric_name VARCHAR,
    metric_value DECIMAL,
    dimensions JSONB,
    date DATE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    UNIQUE(tenant_id, metric_name, date, dimensions)
)
```

## Key Features

### Concurrency Control

- Per-store Redis locks prevent overlapping sync operations
- Queue-based task processing ensures orderly execution

### Error Handling

- Exponential backoff for failed operations
- Dead letter queues for unrecoverable failures
- Comprehensive logging and metrics

### Scalability

- Horizontal scaling via Celery workers
- Database connection pooling
- Async operations for high throughput

### Monitoring

- Sync job status tracking
- Performance metrics collection
- Real-time progress updates

## Configuration

### Environment Variables

```bash
AIRBYTE_API_URL=https://airbyte.your-domain.com
AIRBYTE_API_KEY=your_airbyte_api_key
AIRBYTE_WORKSPACE_ID=your_workspace_id
REDIS_URL=redis://localhost:6379
DATABASE_URL=postgresql://user:pass@localhost:5432/db
```

### Worker Configuration

- Task routing defined in `worker/config.json`
- Prefetch and concurrency settings
- Queue prioritization

## Complete API Endpoint Reference

### Authentication & User Management

```http
POST   /api/auth/login                    # User login
POST   /api/auth/register                 # User registration
POST   /api/auth/refresh                  # Token refresh
GET    /api/auth/me                       # Get current user
POST   /api/auth/forgot-password          # Password reset
```

### Store Management

```http
POST   /api/stores                        # Create store (triggers Airbyte setup)
GET    /api/stores                        # List user's stores
GET    /api/stores/{store_id}             # Get store details
PUT    /api/stores/{store_id}             # Update store settings
DELETE /api/stores/{store_id}             # Remove store
GET    /api/stores/{store_id}/sync-status # Get sync status
```

### Product & Sync Management

```http
GET    /api/products                      # List products with sync status
GET    /api/products/{product_id}         # Get product details
POST   /api/sync/{store_id}               # Trigger manual sync
GET    /api/sync/{store_id}/status        # Get sync job status
POST   /api/sync/{store_id}/incremental   # Incremental sync
GET    /api/sync/{store_id}/history       # Sync job history
```

### Media Generation

```http
POST   /api/media/generate                # Generate media for products
GET    /api/media/jobs                    # List generation jobs
GET    /api/media/jobs/{job_id}           # Get job status & variants
POST   /api/media/regenerate              # Regenerate specific variant
GET    /api/media/variants                # List all variants
GET    /api/media/variants/{variant_id}   # Get variant details
PATCH  /api/media/variants/{variant_id}/favorite # Toggle favorite
PATCH  /api/media/variants/{variant_id}/rating   # Rate variant
```

### Media Publishing

```http
POST   /api/media/push                    # Push variant to platform
GET    /api/media/status/{variant_id}     # Get push status
DELETE /api/media/remove/{variant_id}     # Remove from platform
POST   /api/media/retry/{variant_id}      # Retry failed push
```

### Templates & Voices

```http
GET    /api/media/templates               # Get available templates
GET    /api/media/voices                  # Get available voices
GET    /api/media/voices/list             # List TTS voices
POST   /api/media/voices/test              # Test voice sample
POST   /api/media/voices/generate-script   # Generate narration script
```

### Analytics

```http
GET    /api/analytics/dashboard           # Get dashboard metrics
GET    /api/analytics/product/{id}        # Product-specific analytics
POST   /api/analytics/events              # Track custom events
GET    /api/analytics/performance         # Performance metrics
```

### Webhooks

```http
POST   /api/webhooks/shopify/products/create
POST   /api/webhooks/shopify/products/update
POST   /api/webhooks/shopify/products/delete
POST   /api/webhooks/shopify/orders/create
POST   /api/webhooks/shopify/app/uninstalled
```

### Billing & Usage

```http
GET    /api/billing/usage                 # Get usage statistics
GET    /api/billing/limits                # Get plan limits
POST   /api/billing/upgrade               # Plan upgrade
GET    /api/billing/invoices              # Billing history
```

## Best Practices

### Development

- Use mock data for testing sync flows
- Implement comprehensive logging
- Test webhook processing thoroughly

### Production

- Monitor queue depths and worker health
- Set up alerts for failed syncs
- Regular cleanup of old sync data

### Performance

- Batch processing for large datasets
- Connection pooling for database operations
- Caching for frequently accessed data

## Troubleshooting

### Common Issues & Troubleshooting

#### **Sync & Data Processing Issues**

- **Airbyte connection failures**: Check API credentials, workspace configuration, and network connectivity
- **Webhook signature validation**: Verify Shopify webhook secrets and HMAC signatures
- **Database deadlocks**: Monitor concurrent operations and implement proper locking strategies
- **Sync checkpoint corruption**: Reset checkpoints and trigger full resync if needed
- **Raw data processing failures**: Check Airbyte schema changes and update transformation logic

#### **Media Generation Issues**

- **AI provider rate limits**: Implement exponential backoff and monitor API quotas
- **Video transcoding failures**: Check FFmpeg installation and storage permissions
- **Storage upload failures**: Verify S3/CloudFlare credentials and bucket permissions
- **Provider fallback not working**: Check provider configurations and API keys
- **Generation timeout**: Increase Celery task timeouts for large media files

#### **Media Publishing Issues**

- **Shopify API rate limits**: Implement intelligent retry logic with proper backoff
- **GraphQL mutation failures**: Check product permissions and media upload limits
- **Platform authentication**: Verify OAuth tokens and refresh expired credentials
- **Media deduplication**: Implement proper duplicate detection and handling
- **Cross-platform publishing**: Ensure platform-specific API implementations

#### **Analytics Processing Issues**

- **Event ingestion failures**: Validate event schemas and implement proper error handling
- **Batch processing timeouts**: Adjust batch sizes and processing timeouts
- **Metrics aggregation errors**: Check database constraints and aggregation logic
- **Real-time dashboard delays**: Optimize query performance and caching strategies

#### **Worker & Queue Issues**

- **Celery task timeouts**: Adjust task timeouts based on operation complexity
- **Queue backlog**: Monitor Redis queue depths and scale worker pools
- **Memory exhaustion**: Implement proper resource limits and garbage collection
- **Database connection pools**: Monitor connection usage and implement proper pooling
- **Redis connectivity**: Check Redis cluster health and failover configurations

#### **Performance Issues**

- **Slow media generation**: Optimize AI provider selection and caching
- **High latency APIs**: Implement response caching and database query optimization
- **Storage bottlenecks**: Use CDN distribution and optimize file serving
- **Database query performance**: Add proper indexing and query optimization
- **Memory leaks**: Monitor application memory usage and implement proper cleanup

### Debugging

- Enable detailed logging in worker tasks
- Use Airbyte UI for ETL pipeline inspection
- Monitor Redis for queue status
- Check database for sync job states

## Future Enhancements

- Multi-platform support beyond Shopify
- Advanced sync scheduling
- Real-time sync status WebSocket updates
- Enhanced error recovery mechanisms
- Sync performance analytics

## Migration Notes

### From Per-Store Tables to Shared Tables

If upgrading from the old per-store table approach:

1. **Backup existing data** from prefixed tables
2. **Run migration script** to copy data to shared tables with `store_id`
3. **Update Airbyte connections** to use new configuration
4. **Test sync operations** with sample stores
5. **Monitor performance** and adjust indexing as needed

### Database Indexes for Shared Tables

```sql
-- Recommended indexes for shared tables
CREATE INDEX idx_products_store_id ON raw.products(store_id);
CREATE INDEX idx_products_store_updated ON raw.products(store_id, updated_at);
CREATE INDEX idx_product_variants_store_id ON raw.product_variants(store_id);
CREATE INDEX idx_product_variants_store_product ON raw.product_variants(store_id, product_id);
```

### Performance Considerations

- **Partitioning**: Consider table partitioning by `store_id` for very large datasets
- **Archiving**: Implement data archiving for old sync records
- **Caching**: Cache frequently accessed product data by store

---

## 📋 **Complete Architecture Summary**

### **🎯 System Overview**

The ProductVideo platform is a comprehensive SaaS solution that provides AI-powered video generation for e-commerce stores. The system integrates seamlessly with Shopify for product synchronization, generates multi-modal content (video/image/voice), and publishes content back to e-commerce platforms with full automation.

### **🏗️ Architecture Highlights**

#### **1. Multi-Layer Architecture**
- **Client Layer**: Web dashboard, API clients, Shopify webhooks
- **API Layer**: FastAPI server with authentication, rate limiting, middleware
- **Processing Layer**: Celery workers with Redis queuing and background processors
- **Data Layer**: PostgreSQL, Redis cache, S3/CloudFlare R2 storage
- **External Services**: Airbyte ETL, AI providers, Shopify API

#### **2. Complete Flow Coverage**
- ✅ **Store Onboarding**: Automated Airbyte setup and OAuth configuration
- ✅ **Data Synchronization**: Webhook-driven and manual sync with Airbyte ETL
- ✅ **AI Media Generation**: Multi-provider support with retry logic and fallback
- ✅ **Content Publishing**: Platform-specific publishing with rate limiting
- ✅ **Analytics Processing**: Event tracking and real-time metrics

#### **3. Enterprise-Grade Features**
- ✅ **Scalability**: Horizontal scaling via Celery workers and database partitioning
- ✅ **Reliability**: Comprehensive retry logic, error recovery, and monitoring
- ✅ **Observability**: Prometheus metrics, structured logging, performance tracking
- ✅ **Security**: JWT authentication, encrypted tokens, rate limiting
- ✅ **Multi-tenancy**: Isolated stores with shared infrastructure

### **🔧 Technical Implementation**

#### **Background Processing**
- **4 Specialized Processors**: AirbyteSync, MediaGeneration, MediaPush, Analytics
- **Task Queue Management**: Redis-based queuing with priority levels
- **Error Handling**: Exponential backoff, dead letter queues, status tracking
- **Monitoring**: Real-time metrics and performance dashboards

#### **Data Architecture**
- **Shared Multi-tenant Tables**: Store isolation via `store_id` columns
- **Raw Data Processing**: Airbyte staging tables with transformation pipelines
- **Production Data**: Normalized tables with proper relationships
- **Analytics Storage**: Event tracking with aggregation capabilities

#### **API Design**
- **RESTful Endpoints**: 30+ endpoints covering all platform features
- **Authentication**: JWT-based with role-based access control
- **Rate Limiting**: Built-in protection against API abuse
- **Documentation**: Auto-generated OpenAPI/Swagger documentation

### **🚀 Key Differentiators**

#### **AI Integration**
- **Multi-Provider Support**: Google Veo 3, Banana, TTS with automatic fallback
- **Content Generation**: Video, image, and voice generation in one platform
- **Quality Assurance**: Retry logic and error recovery for reliable generation

#### **E-Commerce Integration**
- **Shopify Native**: OAuth, webhooks, GraphQL API integration
- **Auto-Publishing**: Seamless content publishing to product galleries
- **Real-time Sync**: Webhook-driven updates with manual sync fallback

#### **Developer Experience**
- **Comprehensive Documentation**: Complete API reference and architecture docs
- **Monitoring Tools**: Built-in metrics and logging for operational visibility
- **Extensible Design**: Plugin architecture for additional platforms and providers

### **📊 Business Impact**

#### **For E-Commerce Stores**
- **Automated Content Creation**: AI-generated videos without technical expertise
- **Multi-Channel Publishing**: Content automatically published to Shopify
- **Performance Analytics**: Real-time insights into content performance
- **Scalable Solution**: Handles stores of any size with consistent performance

#### **For the Platform**
- **SaaS Revenue Model**: Subscription-based with usage tracking
- **Operational Excellence**: Automated workflows with minimal manual intervention
- **Scalable Infrastructure**: Cloud-native design supporting rapid growth
- **Enterprise Features**: Multi-tenancy, monitoring, security, compliance

### **🔮 Future Roadmap**

- **Multi-Platform Support**: WooCommerce, BigCommerce, Magento integration
- **Advanced AI Features**: Custom model training, style transfer, personalization
- **Real-time Collaboration**: Team collaboration and approval workflows
- **Advanced Analytics**: Predictive analytics and recommendation engine
- **Mobile Applications**: iOS/Android apps for content management
- **API Marketplace**: Third-party integrations and white-label solutions

---

## 📚 **Documentation Index**

### **Core Documentation**
- ✅ **Architecture Overview**: Complete system design and component interactions
- ✅ **API Reference**: Comprehensive endpoint documentation with examples
- ✅ **Data Models**: Database schema and relationships
- ✅ **Flow Diagrams**: Visual representation of all system flows

### **Operational Documentation**
- ✅ **Setup & Configuration**: Environment variables and deployment guides
- ✅ **Troubleshooting**: Common issues and debugging procedures
- ✅ **Best Practices**: Development and production guidelines
- ✅ **Migration Guide**: Upgrading from legacy systems

### **Developer Documentation**
- ✅ **Component Reference**: Detailed processor and service documentation
- ✅ **Integration Guides**: Third-party service integration procedures
- ✅ **Monitoring Setup**: Metrics and alerting configuration
- ✅ **Testing Strategies**: Unit, integration, and end-to-end testing

This documentation provides a complete reference for understanding, deploying, and maintaining the ProductVideo platform. The architecture is designed for scale, reliability, and maintainability while providing a seamless experience for both developers and end-users.
