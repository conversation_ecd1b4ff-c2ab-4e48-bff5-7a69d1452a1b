# Temporal dynamic configuration for Airbyte development environment
# This file configures Temporal workflow engine settings for optimal performance

# Frontend service configuration
frontend.enableClientVersionCheck:
  - value: true
    constraints: {}

# History service configuration  
history.persistenceMaxQPS:
  - value: 3000
    constraints: {}

history.persistenceGlobalMaxQPS:
  - value: 4000
    constraints: {}

# Matching service configuration
matching.persistenceMaxQPS:
  - value: 3000
    constraints: {}

matching.persistenceGlobalMaxQPS:
  - value: 4000
    constraints: {}

# Worker service configuration
worker.persistenceMaxQPS:
  - value: 3000
    constraints: {}

worker.persistenceGlobalMaxQPS:
  - value: 4000
    constraints: {}

# System configuration
system.enableReadFromClosedExecutionV2:
  - value: true
    constraints: {}

# Workflow execution limits
limit.maxIDLength:
  - value: 1000
    constraints: {}

limit.blobSizeLimitError:
  - value: 2097152
    constraints: {}

limit.blobSizeLimitWarn:
  - value: 262144
    constraints: {}

# Retention configuration
system.defaultWorkflowRetentionTTL:
  - value: "72h"
    constraints: {}

system.maxWorkflowRetentionTTL:
  - value: "2160h"
    constraints: {}

# Visibility configuration
system.advancedVisibilityWritingMode:
  - value: "off"
    constraints: {}

# Search attributes
system.forceSearchAttributesCacheRefreshOnRead:
  - value: false
    constraints: {}
