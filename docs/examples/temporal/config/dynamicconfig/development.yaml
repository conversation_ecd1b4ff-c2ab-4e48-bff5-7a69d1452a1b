frontend.enableClientVersionCheck:
  - value: false

system.forceSearchAttributesCacheRefreshOnRead:
  - value: true

system.enableActivityEagerExecution:
  - value: true

history.persistenceMaxQPS:
  - value: 3000

frontend.persistenceMaxQPS:
  - value: 3000

frontend.throttledLogRPS:
  - value: 20

frontend.serviceConfig:
  rpc:
    grpcMaxConcurrentStreams: 1000
    grpcMaxMessageSize: 4194304

matching.persistenceMaxQPS:
  - value: 3000

worker.persistenceMaxQPS:
  - value: 3000

worker.enableEagerActivityDispatch:
  - value: true