persistence:
  defaultStore: postgres
  visibilityStore: postgres
  numHistoryShards: 1
  datastores:
    postgres:
      sql:
        pluginName: "postgres"
        databaseName: "temporal"
        connectAddr: "db:5432"
        connectProtocol: "tcp"
        user: "app_user"
        password: "dev_password"
        maxConns: 20
        maxConnLifetime: "1h"

global:
  membership:
    maxJoinDuration: 30s
    broadcastAddress: "airbyte-temporal"

services:
  frontend:
    rpc:
      grpcPort: 7233
      membershipPort: 6933
      bindOnLocalHost: true

  history:
    rpc:
      grpcPort: 7234
      membershipPort: 6934
      bindOnLocalHost: true

  matching:
    rpc:
      grpcPort: 7235
      membershipPort: 6935
      bindOnLocalHost: true

  worker:
    rpc:
      grpcPort: 7239
      membershipPort: 6939
      bindOnLocalHost: true

clusterMetadata:
  enableGlobalNamespace: false
  failoverVersionIncrement: 10
  masterClusterName: "active"
  currentClusterName: "active"
  clusterInformation:
    active:
      enabled: true
      initialFailoverVersion: 1
      rpcAddress: "airbyte-temporal:7233"

dcRedirectionPolicy:
  policy: "noop"

archival:
  history:
    state: "disabled"
  visibility:
    state: "disabled"

publicClient:
  hostPort: "airbyte-temporal:7233"