services:
  # PostgreSQL Database (Enhanced for Airbyte)
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: ecommerce_db
      POSTGRES_USER: app_user
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/core/db/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
      - ./scripts/init-airbyte-db.sql:/docker-entrypoint-initdb.d/init-airbyte-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U app_user -d ecommerce_db"]
      interval: 10s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - airbyte_network

  # Redis for job queue and caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    restart: unless-stopped
    networks:
      - airbyte_network

  # Airbyte OSS Services - Modern Setup Following Official Documentation
  # Using latest stable version with proper Temporal integration

  # Airbyte Database (separate from main app database for isolation)
  airbyte-db:
    image: postgres:15-alpine
    restart: unless-stopped
    logging: &default-logging
      options:
        max-size: "100m"
        max-file: "5"
      driver: json-file
    environment:
      POSTGRES_USER: airbyte
      POSTGRES_PASSWORD: airbyte
      POSTGRES_DB: airbyte
    volumes:
      - airbyte_db_data:/var/lib/postgresql/data
    networks:
      - airbyte_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U airbyte -d airbyte"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Temporal Database (required for Airbyte workflow orchestration)
  airbyte-temporal-db:
    image: postgres:15-alpine
    restart: unless-stopped
    logging: *default-logging
    environment:
      POSTGRES_USER: temporal
      POSTGRES_PASSWORD: temporal
      POSTGRES_DB: temporal
    volumes:
      - airbyte_temporal_db_data:/var/lib/postgresql/data
    networks:
      - airbyte_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U temporal -d temporal"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Temporal Service (required for Airbyte workflow orchestration)
  airbyte-temporal:
    image: temporalio/auto-setup:1.22.0
    restart: unless-stopped
    logging: *default-logging
    environment:
      - DB=postgresql
      - DB_PORT=5432
      - POSTGRES_USER=temporal
      - POSTGRES_PWD=temporal
      - POSTGRES_SEEDS=airbyte-temporal-db
      - DYNAMIC_CONFIG_FILE_PATH=config/dynamicconfig/development.yaml
    volumes:
      - ./temporal:/etc/temporal/config/dynamicconfig
    networks:
      - airbyte_network
    depends_on:
      airbyte-temporal-db:
        condition: service_healthy
    healthcheck:
      test:
        [
          "CMD",
          "tctl",
          "--address",
          "airbyte-temporal:7233",
          "workflow",
          "list",
        ]
      interval: 30s
      timeout: 10s
      retries: 5

  # Airbyte Bootloader (initializes database schema)
  airbyte-bootloader:
    image: airbyte/bootloader:1.0.0
    restart: "no"
    logging: *default-logging
    environment:
      - DATABASE_HOST=airbyte-db
      - DATABASE_PORT=5432
      - DATABASE_PASSWORD=airbyte
      - DATABASE_USER=airbyte
      - DATABASE_DB=airbyte
      - LOG_LEVEL=INFO
    networks:
      - airbyte_network
    depends_on:
      airbyte-db:
        condition: service_healthy

  # Airbyte Server (API and orchestration)
  airbyte-server:
    image: airbyte/server:1.0.0
    restart: unless-stopped
    logging: *default-logging
    environment:
      - DATABASE_HOST=airbyte-db
      - DATABASE_PORT=5432
      - DATABASE_PASSWORD=airbyte
      - DATABASE_USER=airbyte
      - DATABASE_DB=airbyte
      - WORKSPACE_ROOT=/tmp/workspace
      - CONFIG_ROOT=/data
      - TRACKING_STRATEGY=logging
      - TEMPORAL_HOST=airbyte-temporal:7233
      - WORKER_ENVIRONMENT=docker
      - LOG_LEVEL=INFO
      - JOB_MAIN_CONTAINER_CPU_REQUEST=
      - JOB_MAIN_CONTAINER_CPU_LIMIT=
      - JOB_MAIN_CONTAINER_MEMORY_REQUEST=
      - JOB_MAIN_CONTAINER_MEMORY_LIMIT=
      - SECRET_PERSISTENCE=TESTING_CONFIG_DB_TABLE
      - USE_STREAM_CAPABLE_STATE=true
      - AUTO_DETECT_SCHEMA=true
    ports:
      - "8001:8001"
    volumes:
      - airbyte_workspace:/tmp/workspace
      - airbyte_data:/data
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - airbyte_network
    depends_on:
      airbyte-bootloader:
        condition: service_completed_successfully
      airbyte-temporal:
        condition: service_healthy

  # Airbyte Worker (executes sync jobs)
  airbyte-worker:
    image: airbyte/worker:1.0.0
    restart: unless-stopped
    logging: *default-logging
    environment:
      - DATABASE_HOST=airbyte-db
      - DATABASE_PORT=5432
      - DATABASE_PASSWORD=airbyte
      - DATABASE_USER=airbyte
      - DATABASE_DB=airbyte
      - WORKSPACE_ROOT=/tmp/workspace
      - LOCAL_ROOT=/tmp/airbyte_local
      - TEMPORAL_HOST=airbyte-temporal:7233
      - WORKER_ENVIRONMENT=docker
      - LOG_LEVEL=INFO
      - JOB_MAIN_CONTAINER_CPU_REQUEST=
      - JOB_MAIN_CONTAINER_CPU_LIMIT=
      - JOB_MAIN_CONTAINER_MEMORY_REQUEST=
      - JOB_MAIN_CONTAINER_MEMORY_LIMIT=
      - SECRET_PERSISTENCE=TESTING_CONFIG_DB_TABLE
      - USE_STREAM_CAPABLE_STATE=true
      - AUTO_DETECT_SCHEMA=true
    volumes:
      - airbyte_workspace:/tmp/workspace
      - airbyte_local:/tmp/airbyte_local
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - airbyte_network
    depends_on:
      airbyte-server:
        condition: service_started

  # Airbyte Web Application
  airbyte-webapp:
    image: airbyte/webapp:1.0.0
    restart: unless-stopped
    logging: *default-logging
    environment:
      - AIRBYTE_SERVER_HOST=airbyte-server
      - AIRBYTE_SERVER_PORT=8001
      - API_URL=/api/v1/
      - CONNECTOR_BUILDER_API_HOST=airbyte-server
      - CONNECTOR_BUILDER_API_PORT=8001
    ports:
      - "8080:80"
    networks:
      - airbyte_network
    depends_on:
      airbyte-server:
        condition: service_started

  # Airbyte Connector Builder Server (for custom connectors)
  airbyte-connector-builder-server:
    image: airbyte/connector-builder-server:1.0.0
    restart: unless-stopped
    logging: *default-logging
    environment:
      - DATABASE_HOST=airbyte-db
      - DATABASE_PORT=5432
      - DATABASE_PASSWORD=airbyte
      - DATABASE_USER=airbyte
      - DATABASE_DB=airbyte
      - AIRBYTE_CDK_VERSION=latest
    volumes:
      - airbyte_workspace:/tmp/workspace
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - airbyte_network
    depends_on:
      airbyte-server:
        condition: service_started

  # MinIO for S3-compatible storage (Enhanced for Airbyte)
  minio:
    image: minio/minio:latest
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - airbyte_network

  # MinIO Client for bucket setup (Enhanced for Airbyte)
  minio-setup:
    image: minio/mc:latest
    depends_on:
      minio:
        condition: service_healthy
    entrypoint: >
      /bin/sh -c "
      mc alias set myminio http://minio:9000 minioadmin minioadmin123;
      mc mb myminio/ecommerce-videos --ignore-existing;
      mc mb myminio/airbyte-logs --ignore-existing;
      mc mb myminio/airbyte-state --ignore-existing;
      mc policy set public myminio/ecommerce-videos;
      mc policy set private myminio/airbyte-logs;
      mc policy set private myminio/airbyte-state;
      echo 'MinIO setup complete';
      "
    restart: on-failure
    networks:
      - airbyte_network

  # ProductVideo Backend API (Enhanced)
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile.api
    ports:
      - "8000:8000"
    env_file:
      - ./backend/.env
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
      - ./storage:/app/storage
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - airbyte_network

  # Unified Worker for all queues (Enhanced)
  worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.worker
    env_file:
      - ./backend/.env
    environment:
      - WORKER_ID=worker-${HOSTNAME:-default}
      # QUEUE_TYPE is intentionally not set, so the worker handles all queues
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
      - ./storage:/app/storage
    command: celery -A servers.worker.main worker --loglevel=info --concurrency=4 --queues=media-generation,media-push,analytics-processing,sync-jobs,sync-orchestration,consumer-processing
    restart: on-failure
    networks:
      - airbyte_network
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # App (Enhanced)
  app:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    env_file:
      - ./frontend/.env
    volumes:
      - ./frontend:/app
      - app_data:/app/node_modules
    depends_on:
      - api
    command: npm run dev -- --port 3000
    restart: unless-stopped
    networks:
      - airbyte_network

  # Database Admin (Development)
  pgweb:
    image: sosedoff/pgweb
    ports:
      - "8081:8081"
    environment:
      DATABASE_URL: ****************************************/ecommerce_db?sslmode=disable
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - airbyte_network

  # Flower Dashboard for Celery (Development)
  flower:
    image: mher/flower:latest
    ports:
      - "5555:5555"
    environment:
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - airbyte_network

  # Prometheus for monitoring (Enhanced)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--storage.tsdb.path=/prometheus"
      - "--web.console.libraries=/etc/prometheus/console_libraries"
      - "--web.console.templates=/etc/prometheus/consoles"
      - "--storage.tsdb.retention.time=200h"
      - "--web.enable-lifecycle"
    restart: unless-stopped
    networks:
      - airbyte_network

  # Monitoring
  # prometheus:
  #   image: prom/prometheus:latest
  #   ports:
  #     - "9090:9090"
  #   volumes:
  #     - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
  #     - prometheus_data:/prometheus
  #   command:
  #     - "--config.file=/etc/prometheus/prometheus.yml"
  #     - "--storage.tsdb.path=/prometheus"
  #     - "--web.console.libraries=/etc/prometheus/console_libraries"
  #     - "--web.console.templates=/etc/prometheus/consoles"
  #     - "--storage.tsdb.retention.time=200h"
  #     - "--web.enable-lifecycle"
  #   restart: unless-stopped

  # grafana:
  #   image: grafana/grafana:latest
  #   ports:
  #     - "3001:3000"
  #   environment:
  #     - GF_SECURITY_ADMIN_PASSWORD=admin
  #   volumes:
  #     - grafana_data:/var/lib/grafana
  #   restart: unless-stopped

  # # Mailhog for email testing
  # mailhog:
  #   image: mailhog/mailhog:latest
  #   ports:
  #     - "1025:1025" # SMTP
  #     - "8025:8025" # Web UI
  #   restart: unless-stopped

  # Nginx for reverse proxy and static files
  # nginx:
  #   image: nginx:alpine
  #   ports:
  #     - "80:80"
  #     - "443:443" # For HTTPS, if configured
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf
  #     # Mount backend static files if needed by Nginx
  #     - ./backend/storage:/var/www/static # Assuming backend static files are in backend/storage
  #     # Mount .htpasswd if used for admin panel
  #     # - ./nginx/.htpasswd:/etc/nginx/.htpasswd # You'll need to create this file
  #   depends_on:
  #     - api
  #     - app
  #     - minio # If Nginx proxies to MinIO directly
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost/health"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #   restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  minio_data:
  app_data:
  prometheus_data:
  grafana_data:
  # Airbyte volumes
  airbyte_db_data:
  airbyte_temporal_db_data:
  airbyte_workspace:
  airbyte_data:
  airbyte_local:

networks:
  airbyte_network:
    name: airbyte_network
    driver: bridge
