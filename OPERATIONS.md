# Operations Runbook: Airbyte Shopify Sync System

This runbook provides operational procedures for managing the Airbyte Shopify Sync system in production.

## 🚨 Emergency Procedures

### System Down - Complete Outage

**Symptoms**: All services unresponsive, no webhook processing

**Immediate Actions**:
1. Check Docker services status
2. Verify database and Redis connectivity
3. Check system resources (CPU, memory, disk)
4. Review recent deployments or changes

```bash
# Quick health check
docker-compose ps
curl -f http://localhost:8002/health
curl -f http://localhost:8003/health
curl -f http://localhost:8004/health

# Check system resources
df -h
free -m
top

# Restart all services if needed
docker-compose restart
```

### Webhook Processing Stopped

**Symptoms**: No new webhook events being processed

**Diagnosis**:
```bash
# Check webhook receiver health
curl http://localhost:8002/health

# Check recent webhook metrics
curl http://localhost:8002/metrics | grep webhook_requests_total

# Check webhook queue size
docker-compose exec redis redis-cli llen webhook_processing_normal
```

**Resolution**:
```bash
# Restart webhook receiver
docker-compose restart webhook-receiver

# Clear stuck jobs if needed
docker-compose exec redis redis-cli flushdb

# Verify processing resumes
curl http://localhost:8002/metrics | grep webhook_requests_total
```

### Sync Jobs Stuck

**Symptoms**: Active syncs but no completion for extended period

**Diagnosis**:
```bash
# Check orchestration worker status
curl http://localhost:8003/status

# Check Airbyte job status
curl http://localhost:8001/api/v1/jobs/list \
  -H "Content-Type: application/json" \
  -d '{"configTypes": ["sync"], "pagination": {"pageSize": 10}}'

# Check database for stuck jobs
psql -c "SELECT * FROM sync_jobs WHERE status = 'running' AND started_at < NOW() - INTERVAL '2 hours';"
```

**Resolution**:
```bash
# Restart orchestration worker
docker-compose restart orchestration-worker

# Cancel stuck Airbyte jobs if needed
curl -X POST http://localhost:8001/api/v1/jobs/cancel \
  -H "Content-Type: application/json" \
  -d '{"id": STUCK_JOB_ID}'

# Reset stuck sync jobs
psql -c "UPDATE sync_jobs SET status = 'failed', error_message = 'Manually reset due to timeout' WHERE status = 'running' AND started_at < NOW() - INTERVAL '2 hours';"
```

## 📊 Monitoring and Alerting

### Key Metrics to Monitor

#### Service Health
- Service uptime and response times
- Memory and CPU usage
- Database connection pool status
- Redis connection status

#### Business Metrics
- Webhook processing rate and latency
- Sync job success/failure rates
- Data processing throughput
- Queue depths and processing lag

#### Error Metrics
- HMAC validation failures
- API rate limit hits
- Database transaction failures
- Data validation errors

### Alert Thresholds

```yaml
# Prometheus alerting rules
- alert: WebhookProcessingLag
  expr: webhook_processing_lag_seconds > 300
  for: 5m
  severity: warning

- alert: SyncJobFailureRate
  expr: rate(sync_jobs_total{status="failed"}[10m]) / rate(sync_jobs_total[10m]) > 0.2
  for: 5m
  severity: critical

- alert: StagingBacklog
  expr: staging_records_pending > 1000
  for: 10m
  severity: warning
```

### Monitoring Dashboards

Access Grafana dashboards at `http://localhost:3001`:
- **System Overview**: Service health and resource usage
- **Business Metrics**: Sync performance and data flow
- **Error Analysis**: Error rates and failure patterns

## 🔧 Routine Maintenance

### Daily Tasks

#### Health Checks
```bash
# Automated health check script
#!/bin/bash
services=("webhook-receiver" "orchestration-worker" "consumer-service")

for service in "${services[@]}"; do
    if curl -f http://localhost:800$((${#service}+1))/health > /dev/null 2>&1; then
        echo "✅ $service: healthy"
    else
        echo "❌ $service: unhealthy"
    fi
done
```

#### Queue Monitoring
```bash
# Check queue depths
docker-compose exec redis redis-cli llen webhook_processing_high
docker-compose exec redis redis-cli llen webhook_processing_normal
docker-compose exec redis redis-cli llen webhook_processing_low

# Check for dead letter queue items
psql -c "SELECT COUNT(*) FROM dead_letter_queue WHERE resolved = false;"
```

### Weekly Tasks

#### Database Maintenance
```bash
# Vacuum and analyze tables
psql -c "VACUUM ANALYZE webhook_events;"
psql -c "VACUUM ANALYZE sync_jobs;"
psql -c "VACUUM ANALYZE staging_products;"
psql -c "VACUUM ANALYZE staging_orders;"
psql -c "VACUUM ANALYZE staging_customers;"

# Check table sizes
psql -c "SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size FROM pg_tables WHERE schemaname = 'public' ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;"
```

#### Log Rotation
```bash
# Rotate application logs
docker-compose exec webhook-receiver logrotate /etc/logrotate.conf
docker-compose exec orchestration-worker logrotate /etc/logrotate.conf
docker-compose exec consumer-service logrotate /etc/logrotate.conf

# Clean old Docker logs
docker system prune -f
```

### Monthly Tasks

#### Performance Review
- Analyze sync performance trends
- Review error patterns and root causes
- Assess resource utilization and scaling needs
- Update capacity planning

#### Security Review
- Rotate webhook secrets
- Review access logs
- Update dependencies
- Security scan containers

## 🔍 Troubleshooting Guide

### Performance Issues

#### High Memory Usage
```bash
# Check memory usage by service
docker stats

# Check for memory leaks
docker-compose exec webhook-receiver ps aux --sort=-%mem | head -10

# Restart services if needed
docker-compose restart webhook-receiver
```

#### Slow Database Queries
```bash
# Enable query logging
psql -c "ALTER SYSTEM SET log_statement = 'all';"
psql -c "SELECT pg_reload_conf();"

# Check slow queries
psql -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"

# Check for missing indexes
psql -c "SELECT schemaname, tablename, attname, n_distinct, correlation FROM pg_stats WHERE schemaname = 'public' AND n_distinct > 100;"
```

### Data Issues

#### Missing Data
```bash
# Check sync checkpoints
psql -c "SELECT * FROM sync_checkpoints ORDER BY last_successful_sync_at;"

# Check for failed sync jobs
psql -c "SELECT * FROM sync_jobs WHERE status = 'failed' ORDER BY created_at DESC LIMIT 10;"

# Check staging table processing
psql -c "SELECT COUNT(*) FROM staging_products WHERE processed = false;"
```

#### Data Inconsistencies
```bash
# Compare record counts
psql -c "SELECT 'products' as entity, COUNT(*) FROM products UNION ALL SELECT 'orders', COUNT(*) FROM orders UNION ALL SELECT 'customers', COUNT(*) FROM customers;"

# Check for duplicate records
psql -c "SELECT external_id, COUNT(*) FROM products GROUP BY external_id HAVING COUNT(*) > 1;"

# Verify data freshness
psql -c "SELECT MAX(updated_at) as latest_update FROM products;"
```

## 🚀 Scaling Procedures

### Horizontal Scaling

#### Scale Webhook Receivers
```bash
# Add more webhook receiver instances
docker-compose up -d --scale webhook-receiver=3

# Configure load balancer to distribute traffic
# (Implementation depends on your load balancer)
```

#### Scale Consumer Services
```bash
# Add more consumer instances
docker-compose up -d --scale consumer-service=3

# Monitor processing improvement
curl http://localhost:8004/metrics | grep records_processed_total
```

### Vertical Scaling

#### Increase Resource Limits
```yaml
# docker-compose.yml
services:
  webhook-receiver:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
```

#### Database Scaling
```bash
# Increase connection pool size
export DATABASE_MAX_CONNECTIONS=50

# Add read replicas for reporting
# (Implementation depends on your database setup)
```

## 🔐 Security Procedures

### Webhook Secret Rotation

```bash
# 1. Generate new secret
NEW_SECRET=$(openssl rand -hex 32)

# 2. Update environment variable
echo "SHOPIFY_WEBHOOK_SECRET=$NEW_SECRET" >> .env

# 3. Restart webhook receiver
docker-compose restart webhook-receiver

# 4. Update Shopify webhook configuration
# (Done through Shopify Admin or API)

# 5. Verify new secret works
curl -X POST http://localhost:8002/webhooks/shopify/test-shop.myshopify.com \
  -H "X-Shopify-Hmac-Sha256: $(echo -n 'test' | openssl dgst -sha256 -hmac '$NEW_SECRET' -binary | base64)" \
  -d 'test'
```

### Access Token Rotation

```bash
# 1. Generate new Shopify access token
# (Done through Shopify Admin or Partner Dashboard)

# 2. Update Airbyte source configuration
python airbyte/setup_connections.py \
  --shop-domain your-shop.myshopify.com \
  --access-token NEW_ACCESS_TOKEN

# 3. Test connection
curl -X POST http://localhost:8001/api/v1/sources/check_connection \
  -H "Content-Type: application/json" \
  -d '{"sourceId": "YOUR_SOURCE_ID"}'
```

## 📋 Backup and Recovery

### Database Backup

```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="backup_${DATE}.sql"

pg_dump -h localhost -U app_user ecommerce_db > $BACKUP_FILE
gzip $BACKUP_FILE

# Upload to S3 or backup storage
aws s3 cp ${BACKUP_FILE}.gz s3://your-backup-bucket/database/

# Clean old backups (keep 30 days)
find . -name "backup_*.sql.gz" -mtime +30 -delete
```

### Configuration Backup

```bash
# Backup Airbyte configuration
docker-compose exec airbyte-server tar -czf /tmp/airbyte_config.tar.gz /data
docker cp airbyte-server:/tmp/airbyte_config.tar.gz ./airbyte_config_backup.tar.gz

# Backup environment configuration
cp .env .env.backup.$(date +%Y%m%d)
```

### Disaster Recovery

```bash
# Full system recovery procedure
# 1. Restore infrastructure
docker-compose up -d

# 2. Restore database
gunzip -c backup_latest.sql.gz | psql -h localhost -U app_user ecommerce_db

# 3. Restore Airbyte configuration
docker cp airbyte_config_backup.tar.gz airbyte-server:/tmp/
docker-compose exec airbyte-server tar -xzf /tmp/airbyte_config_backup.tar.gz -C /

# 4. Restart services
docker-compose restart

# 5. Verify system health
./health_check.sh
```

## 📞 Escalation Procedures

### Severity Levels

**P0 - Critical**: Complete system outage, data loss
- Immediate response required
- Escalate to on-call engineer
- Notify business stakeholders

**P1 - High**: Partial outage, significant performance degradation
- Response within 1 hour
- Escalate if not resolved in 2 hours

**P2 - Medium**: Minor issues, workarounds available
- Response within 4 hours
- Standard troubleshooting procedures

**P3 - Low**: Enhancement requests, minor bugs
- Response within 24 hours
- Standard development process

### Contact Information

- **On-call Engineer**: [Contact details]
- **Database Administrator**: [Contact details]
- **Infrastructure Team**: [Contact details]
- **Business Stakeholders**: [Contact details]

### Communication Channels

- **Incident Response**: #incident-response
- **System Alerts**: #system-alerts
- **General Support**: #shopify-sync-support
