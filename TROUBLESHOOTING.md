# Troubleshooting Guide: Airbyte Shopify Sync System

This guide helps diagnose and resolve common issues with the Airbyte Shopify Sync system.

## 🔍 Quick Diagnosis

### System Health Check

Run this comprehensive health check first:

```bash
#!/bin/bash
echo "🏥 System Health Check"
echo "====================="

# Check Docker services
echo "📦 Docker Services:"
docker-compose ps

# Check service endpoints
services=("8002:webhook-receiver" "8003:orchestration-worker" "8004:consumer-service" "8080:airbyte-webapp")
for service in "${services[@]}"; do
    port=$(echo $service | cut -d: -f1)
    name=$(echo $service | cut -d: -f2)
    if curl -f http://localhost:$port/health > /dev/null 2>&1; then
        echo "✅ $name: healthy"
    else
        echo "❌ $name: unhealthy"
    fi
done

# Check database connectivity
if psql -h localhost -U app_user -d ecommerce_db -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database: connected"
else
    echo "❌ Database: connection failed"
fi

# Check Redis connectivity
if docker-compose exec redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis: connected"
else
    echo "❌ Redis: connection failed"
fi

# Check recent activity
echo "📊 Recent Activity:"
psql -h localhost -U app_user -d ecommerce_db -c "
SELECT 
    'Webhooks (last hour)' as metric,
    COUNT(*) as count
FROM webhook_events 
WHERE received_at > NOW() - INTERVAL '1 hour'
UNION ALL
SELECT 
    'Sync jobs (last hour)' as metric,
    COUNT(*) as count
FROM sync_jobs 
WHERE created_at > NOW() - INTERVAL '1 hour';
"
```

## 🚨 Common Issues and Solutions

### 1. Webhook Receiver Issues

#### Issue: Webhooks Returning 401 Unauthorized

**Symptoms**:
- Shopify webhook delivery failures
- 401 responses in webhook receiver logs
- HMAC verification failures

**Diagnosis**:
```bash
# Check webhook secret configuration
echo $SHOPIFY_WEBHOOK_SECRET

# Check recent HMAC failures
curl http://localhost:8002/metrics | grep webhook_hmac_failures_total

# Test HMAC verification manually
echo -n '{"test":"data"}' | openssl dgst -sha256 -hmac 'your-webhook-secret' -binary | base64
```

**Solutions**:
1. Verify webhook secret matches Shopify configuration
2. Check for special characters in secret that need escaping
3. Ensure webhook secret is properly set in environment

```bash
# Update webhook secret
export SHOPIFY_WEBHOOK_SECRET="your-correct-secret"
docker-compose restart webhook-receiver

# Test with correct secret
curl -X POST http://localhost:8002/webhooks/shopify/test-shop.myshopify.com \
  -H "Content-Type: application/json" \
  -H "X-Shopify-Topic: products/update" \
  -H "X-Shopify-Hmac-Sha256: $(echo -n '{"test":"data"}' | openssl dgst -sha256 -hmac 'your-correct-secret' -binary | base64)" \
  -d '{"test":"data"}'
```

#### Issue: Webhook Processing Lag

**Symptoms**:
- High `webhook_processing_lag_seconds` metric
- Webhooks queuing up in Redis
- Delayed sync triggering

**Diagnosis**:
```bash
# Check queue depths
docker-compose exec redis redis-cli llen webhook_processing_high
docker-compose exec redis redis-cli llen webhook_processing_normal
docker-compose exec redis redis-cli llen webhook_processing_low

# Check processing metrics
curl http://localhost:8002/metrics | grep webhook_processing_duration
```

**Solutions**:
1. Scale webhook receiver instances
2. Increase processing concurrency
3. Optimize database queries

```bash
# Scale webhook receivers
docker-compose up -d --scale webhook-receiver=3

# Check if processing improves
watch "curl -s http://localhost:8002/metrics | grep webhook_processing_lag"
```

### 2. Orchestration Worker Issues

#### Issue: Sync Jobs Not Starting

**Symptoms**:
- Webhook events processed but no sync jobs created
- Zero `sync_jobs_total` metrics
- No Airbyte sync activity

**Diagnosis**:
```bash
# Check orchestration worker logs
docker-compose logs orchestration-worker | tail -50

# Check webhook events status
psql -c "SELECT topic, status, sync_triggered, COUNT(*) FROM webhook_events WHERE received_at > NOW() - INTERVAL '1 hour' GROUP BY topic, status, sync_triggered;"

# Check Airbyte connectivity
curl http://localhost:8001/api/v1/health
```

**Solutions**:
1. Verify Airbyte connection configuration
2. Check shop-to-connection mapping
3. Restart orchestration worker

```bash
# Check Airbyte connections
curl -X POST http://localhost:8001/api/v1/connections/list \
  -H "Content-Type: application/json" \
  -d '{"workspaceId": "YOUR_WORKSPACE_ID"}'

# Restart orchestration worker
docker-compose restart orchestration-worker

# Manually trigger sync for testing
curl -X POST http://localhost:8003/api/trigger-sync \
  -H "Content-Type: application/json" \
  -d '{"shop_domain": "test-shop.myshopify.com", "entity": "products"}'
```

#### Issue: Sync Jobs Stuck in Running State

**Symptoms**:
- High `active_syncs` metric
- Sync jobs with old `started_at` timestamps
- No completion events

**Diagnosis**:
```bash
# Check stuck sync jobs
psql -c "SELECT id, shop_id, entity, status, started_at, airbyte_job_id FROM sync_jobs WHERE status = 'running' AND started_at < NOW() - INTERVAL '2 hours';"

# Check Airbyte job status
curl -X POST http://localhost:8001/api/v1/jobs/get \
  -H "Content-Type: application/json" \
  -d '{"id": AIRBYTE_JOB_ID}'
```

**Solutions**:
1. Cancel stuck Airbyte jobs
2. Reset sync job status
3. Restart orchestration worker

```bash
# Cancel stuck Airbyte job
curl -X POST http://localhost:8001/api/v1/jobs/cancel \
  -H "Content-Type: application/json" \
  -d '{"id": STUCK_AIRBYTE_JOB_ID}'

# Reset stuck sync jobs
psql -c "UPDATE sync_jobs SET status = 'failed', error_message = 'Reset due to timeout', finished_at = NOW() WHERE status = 'running' AND started_at < NOW() - INTERVAL '2 hours';"

# Restart orchestration worker
docker-compose restart orchestration-worker
```

### 3. Consumer Service Issues

#### Issue: Staging Data Not Being Processed

**Symptoms**:
- High `staging_records_pending` metric
- Records stuck in staging tables
- No production table updates

**Diagnosis**:
```bash
# Check staging table counts
psql -c "SELECT 'products' as entity, COUNT(*) as total, COUNT(*) FILTER (WHERE processed = false) as pending FROM staging_products UNION ALL SELECT 'orders', COUNT(*), COUNT(*) FILTER (WHERE processed = false) FROM staging_orders UNION ALL SELECT 'customers', COUNT(*), COUNT(*) FILTER (WHERE processed = false) FROM staging_customers;"

# Check consumer service logs
docker-compose logs consumer-service | tail -50

# Check processing metrics
curl http://localhost:8004/metrics | grep records_processed_total
```

**Solutions**:
1. Restart consumer service
2. Check for data transformation errors
3. Scale consumer instances

```bash
# Restart consumer service
docker-compose restart consumer-service

# Scale consumer instances
docker-compose up -d --scale consumer-service=3

# Check for processing errors
psql -c "SELECT processing_error, COUNT(*) FROM staging_products WHERE processing_error IS NOT NULL GROUP BY processing_error;"
```

#### Issue: Data Transformation Errors

**Symptoms**:
- Records marked as processed but not in production tables
- Transformation error messages in logs
- Data validation failures

**Diagnosis**:
```bash
# Check for transformation errors
psql -c "SELECT external_id, processing_error FROM staging_products WHERE processing_error IS NOT NULL LIMIT 10;"

# Check data format issues
psql -c "SELECT raw_data FROM staging_products WHERE processed = false LIMIT 1;"

# Validate data structure
psql -c "SELECT jsonb_typeof(raw_data), COUNT(*) FROM staging_products GROUP BY jsonb_typeof(raw_data);"
```

**Solutions**:
1. Fix data transformation logic
2. Handle edge cases in data format
3. Add data validation

```bash
# Reprocess failed records after fixing transformation
psql -c "UPDATE staging_products SET processed = false, processing_error = NULL WHERE processing_error IS NOT NULL;"

# Monitor reprocessing
watch "curl -s http://localhost:8004/metrics | grep records_processed_total"
```

### 4. Database Issues

#### Issue: Database Connection Pool Exhaustion

**Symptoms**:
- "too many connections" errors
- Service timeouts
- High database connection count

**Diagnosis**:
```bash
# Check active connections
psql -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';"

# Check connection by application
psql -c "SELECT application_name, count(*) FROM pg_stat_activity GROUP BY application_name;"

# Check max connections
psql -c "SHOW max_connections;"
```

**Solutions**:
1. Increase max_connections
2. Optimize connection pooling
3. Fix connection leaks

```bash
# Increase max connections (requires restart)
psql -c "ALTER SYSTEM SET max_connections = 200;"
docker-compose restart db

# Check for connection leaks in application
docker-compose logs webhook-receiver | grep -i "connection"
```

#### Issue: Slow Database Queries

**Symptoms**:
- High response times
- Database CPU usage spikes
- Query timeouts

**Diagnosis**:
```bash
# Enable slow query logging
psql -c "ALTER SYSTEM SET log_min_duration_statement = 1000;"
psql -c "SELECT pg_reload_conf();"

# Check slow queries
psql -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"

# Check for missing indexes
psql -c "SELECT schemaname, tablename, attname FROM pg_stats WHERE schemaname = 'public' AND n_distinct > 100 AND correlation < 0.1;"
```

**Solutions**:
1. Add missing indexes
2. Optimize query patterns
3. Update table statistics

```bash
# Add common indexes
psql -c "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_events_shop_topic ON webhook_events(shop_id, topic);"
psql -c "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sync_jobs_status_created ON sync_jobs(status, created_at);"

# Update statistics
psql -c "ANALYZE;"
```

### 5. Airbyte Issues

#### Issue: Airbyte Connector Failures

**Symptoms**:
- Sync jobs fail immediately
- Airbyte UI shows connector errors
- Source connection test failures

**Diagnosis**:
```bash
# Check Airbyte server logs
docker-compose logs airbyte-server | tail -50

# Test source connection
curl -X POST http://localhost:8001/api/v1/sources/check_connection \
  -H "Content-Type: application/json" \
  -d '{"sourceId": "YOUR_SOURCE_ID"}'

# Check connector version
curl -X POST http://localhost:8001/api/v1/source_definitions/list
```

**Solutions**:
1. Update connector version
2. Fix source configuration
3. Check Shopify API credentials

```bash
# Update source configuration
python airbyte/setup_connections.py \
  --shop-domain your-shop.myshopify.com \
  --access-token your-updated-token

# Restart Airbyte services
docker-compose restart airbyte-server airbyte-worker
```

## 🔧 Advanced Troubleshooting

### Performance Analysis

#### Memory Usage Analysis
```bash
# Check memory usage by service
docker stats --no-stream

# Check for memory leaks
docker-compose exec webhook-receiver ps aux --sort=-%mem | head -10

# Monitor memory over time
watch "docker stats --no-stream | grep -E '(webhook|orchestration|consumer)'"
```

#### Database Performance Analysis
```bash
# Check table sizes
psql -c "SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size FROM pg_tables WHERE schemaname = 'public' ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;"

# Check index usage
psql -c "SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch FROM pg_stat_user_indexes ORDER BY idx_scan DESC;"

# Check for bloated tables
psql -c "SELECT schemaname, tablename, n_dead_tup, n_live_tup, ROUND(n_dead_tup::numeric / (n_live_tup + n_dead_tup) * 100, 2) as dead_ratio FROM pg_stat_user_tables WHERE n_live_tup > 0 ORDER BY dead_ratio DESC;"
```

### Network Connectivity Issues

```bash
# Test external connectivity
curl -I https://your-shop.myshopify.com/admin/api/2023-10/products.json

# Test internal service connectivity
docker-compose exec webhook-receiver curl -f http://orchestration-worker:8000/health

# Check DNS resolution
docker-compose exec webhook-receiver nslookup airbyte-server
```

### Log Analysis

```bash
# Search for specific errors
docker-compose logs webhook-receiver | grep -i "error\|exception\|failed"

# Analyze error patterns
docker-compose logs orchestration-worker | grep -E "(ERROR|CRITICAL)" | awk '{print $4}' | sort | uniq -c | sort -nr

# Check for rate limiting
docker-compose logs orchestration-worker | grep -i "rate\|429\|throttle"
```

## 📞 When to Escalate

### Escalation Criteria

**Immediate Escalation (P0)**:
- Complete system outage (all services down)
- Data corruption or loss
- Security breach indicators
- Shopify API access revoked

**Escalate within 1 hour (P1)**:
- Single service completely down
- Sync jobs failing for all shops
- Database connectivity issues
- Significant performance degradation (>50% slower)

**Escalate within 4 hours (P2)**:
- Intermittent failures affecting multiple shops
- Performance issues affecting specific entities
- High error rates (>10% failure rate)
- Monitoring/alerting system failures

### Escalation Information to Provide

1. **Issue Description**: Clear description of the problem
2. **Impact Assessment**: Which shops/entities are affected
3. **Timeline**: When the issue started
4. **Troubleshooting Steps**: What has been tried
5. **Current Status**: System state and any workarounds
6. **Logs and Metrics**: Relevant log excerpts and metric screenshots

### Emergency Contacts

- **On-call Engineer**: [Contact information]
- **Database Team**: [Contact information]
- **Infrastructure Team**: [Contact information]
- **Shopify Technical Support**: [Contact information]

Remember: **When in doubt, escalate early**. It's better to involve additional resources than to let an issue impact business operations.
