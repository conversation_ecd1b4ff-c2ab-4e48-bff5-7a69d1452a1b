#!/usr/bin/env python3
"""
Generic Airbyte Setup Script for Shopify Stores

This script sets up Airbyte integration for any Shopify store by:
1. Creating/checking Airbyte workspace
2. Creating Shopify source for the store
3. Creating PostgreSQL destination
4. Creating connection between source and destination
5. Updating store record in database with Airbyte IDs

Requirements:
- Docker Compose services running (Airbyte, PostgreSQL)
- uv package manager installed

Usage:
    # From backend directory
    uv run python scripts/setup_airbyte_store.py --store-id 2 --shop-domain "dancing-queens-staging.myshopify.com" --access-token "shpat_xxx"

    # Or with environment variables
    export SHOPIFY_STORE_ID=2
    export SHOPIFY_SHOP_DOMAIN="dancing-queens-staging.myshopify.com"
    export SHOPIFY_ACCESS_TOKEN="shpat_xxx"
    uv run python scripts/setup_airbyte_store.py

Environment:
- Requires Airbyte services to be running via docker-compose
- Database connection to PostgreSQL container
- Airbyte API accessible at localhost:8001
"""

import argparse
import json
import os
import sys
import time
import requests
from datetime import datetime, timezone
from sqlalchemy import create_engine, text

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://app_user:dev_password@localhost:5432/ecommerce_db")

# Airbyte configuration
AIRBYTE_API_URL = os.getenv("AIRBYTE_API_URL", "http://localhost:8001/api/v1")
AIRBYTE_USERNAME = os.getenv("AIRBYTE_USERNAME", "airbyte")
AIRBYTE_PASSWORD = os.getenv("AIRBYTE_PASSWORD", "password")

# Template paths (relative to backend directory)
SOURCE_TEMPLATE = "src/core/configs/airbyte_source_shopify.json"
CONNECTION_TEMPLATE = "src/core/configs/airbyte_connection_products.json"


class AirbyteSetup:
    """Handles Airbyte setup for Shopify stores."""

    def __init__(self):
        self.session = requests.Session()
        self.session.auth = (AIRBYTE_USERNAME, AIRBYTE_PASSWORD)
        self.workspace_id = None

    def wait_for_airbyte(self, max_retries: int = 60, delay: int = 10) -> bool:
        """Wait for Airbyte server to be ready with exponential backoff."""
        print("Waiting for Airbyte server to be ready...")

        for attempt in range(max_retries):
            try:
                response = self.session.get(f"{AIRBYTE_API_URL}/health", timeout=10)
                if response.status_code == 200:
                    print("✓ Airbyte server is ready!")
                    return True
            except requests.exceptions.ConnectionError as e:
                if "Connection reset by peer" in str(e):
                    print(f"Attempt {attempt + 1}: Connection reset, server may not be ready yet")
                else:
                    print(f"Attempt {attempt + 1}: Connection error: {e}")
            except requests.exceptions.RequestException as e:
                print(f"Attempt {attempt + 1}: {e}")

            if attempt < max_retries - 1:
                # Exponential backoff with max delay of 30 seconds
                current_delay = min(delay * (2 ** (attempt // 5)), 30)
                print(f"Waiting {current_delay} seconds before retry...")
                time.sleep(current_delay)

        print("✗ Airbyte server is not ready after maximum retries")
        return False

    def create_workspace(self, name: str = "Shopify E-commerce Sync") -> str:
        """Create or get existing workspace."""
        # First, try to list existing workspaces
        try:
            response = self.session.post(f"{AIRBYTE_API_URL}/workspaces/list")
            if response.status_code == 200:
                workspaces = response.json().get("workspaces", [])
                for workspace in workspaces:
                    if workspace["name"] == name:
                        self.workspace_id = workspace["workspaceId"]
                        print(f"✓ Using existing workspace: {self.workspace_id}")
                        return self.workspace_id
        except Exception as e:
            print(f"Warning: Could not list workspaces: {e}")

        # Create new workspace
        workspace_data = {
            "name": name,
            "slug": "shopify-ecommerce-sync",
            "initialSetupComplete": True,
            "displaySetupWizard": False,
            "anonymousDataCollection": False,
            "news": False,
            "securityUpdates": True
        }

        try:
            response = self.session.post(f"{AIRBYTE_API_URL}/workspaces/create", json=workspace_data)
            if response.status_code == 200:
                self.workspace_id = response.json()["workspaceId"]
                print(f"✓ Created new workspace: {self.workspace_id}")
                return self.workspace_id
            else:
                print(f"✗ Failed to create workspace: {response.text}")
                return None
        except Exception as e:
            print(f"✗ Error creating workspace: {e}")
            return None

    def create_shopify_source(self, shop_domain: str, access_token: str, shop_name: str) -> str:
        """Create Shopify source for the store."""
        # Load source template
        with open(SOURCE_TEMPLATE, 'r') as f:
            source_config = json.load(f)

        # Replace placeholders
        source_config["workspaceId"] = self.workspace_id
        source_config["connectionConfiguration"]["shop"] = shop_domain
        source_config["connectionConfiguration"]["credentials"]["api_password"] = access_token
        source_config["connectionConfiguration"]["start_date"] = datetime.now(timezone.utc).strftime("%Y-%m-%d")
        source_config["name"] = f"Shopify Source - {shop_name}"

        try:
            response = self.session.post(f"{AIRBYTE_API_URL}/sources/create", json=source_config)
            if response.status_code == 200:
                source_id = response.json()["sourceId"]
                print(f"✓ Created Shopify source: {source_id}")
                return source_id
            else:
                print(f"✗ Failed to create source: {response.text}")
                return None
        except Exception as e:
            print(f"✗ Error creating source: {e}")
            return None

    def create_postgres_destination(self, shop_name: str) -> str:
        """Create PostgreSQL destination."""
        destination_config = {
            "destinationDefinitionId": "25c5221d-dce2-4163-ade9-739ef790f503",  # Postgres
            "workspaceId": self.workspace_id,
            "connectionConfiguration": {
                "host": "db",
                "port": 5432,
                "database": "ecommerce_db",
                "schema": "public",
                "username": "app_user",
                "password": "dev_password",
                "ssl_mode": {"mode": "disable"},
                "tunnel_method": {"tunnel_method": "NO_TUNNEL"},
                "raw_data_schema": "raw",
                "disable_type_dedupe": False
            },
            "name": f"PostgreSQL Destination - {shop_name}",
            "destinationName": "Postgres"
        }

        try:
            response = self.session.post(f"{AIRBYTE_API_URL}/destinations/create", json=destination_config)
            if response.status_code == 200:
                dest_id = response.json()["destinationId"]
                print(f"✓ Created PostgreSQL destination: {dest_id}")
                return dest_id
            else:
                print(f"✗ Failed to create destination: {response.text}")
                return None
        except Exception as e:
            print(f"✗ Error creating destination: {e}")
            return None

    def discover_source_schema(self, source_id: str):
        """Discover source schema."""
        try:
            response = self.session.post(
                f"{AIRBYTE_API_URL}/sources/discover_schema",
                json={"sourceId": source_id},
                timeout=120  # Increased timeout for schema discovery
            )
            if response.status_code == 200:
                catalog = response.json()["catalog"]
                print("✓ Successfully discovered source schema")
                return catalog
            else:
                print(f"✗ Failed to discover schema: {response.text}")
                return None
        except Exception as e:
            print(f"✗ Error discovering schema: {e}")
            return None

    def create_connection(self, source_id: str, destination_id: str, catalog, shop_name: str):
        """Create connection between source and destination."""
        # Load connection template
        with open(CONNECTION_TEMPLATE, 'r') as f:
            connection_config = json.load(f)

        # Replace placeholders
        connection_config["name"] = f"Shopify Products Sync - {shop_name}"
        connection_config["sourceId"] = source_id
        connection_config["destinationId"] = destination_id

        # Use the discovered catalog
        connection_config["syncCatalog"] = catalog

        try:
            response = self.session.post(f"{AIRBYTE_API_URL}/connections/create", json=connection_config)
            if response.status_code == 200:
                conn_id = response.json()["connectionId"]
                print(f"✓ Created connection: {conn_id}")
                return conn_id
            else:
                print(f"✗ Failed to create connection: {response.text}")
                return None
        except Exception as e:
            print(f"✗ Error creating connection: {e}")
            return None

    def update_store_record(self, store_id: int, source_id: str, destination_id: str, connection_id: str):
        """Update store record with Airbyte IDs."""
        try:
            engine = create_engine(DATABASE_URL)
            with engine.connect() as conn:
                update_query = text("""
                UPDATE stores
                SET airbyte_source_id = :source_id,
                    airbyte_destination_id = :destination_id,
                    airbyte_connection_id = :connection_id,
                    updated_at = NOW()
                WHERE id = :store_id
                """)
                conn.execute(update_query, {
                    "source_id": source_id,
                    "destination_id": destination_id,
                    "connection_id": connection_id,
                    "store_id": store_id
                })
                conn.commit()
                print(f"✓ Updated store {store_id} record with Airbyte IDs")
                return True
        except Exception as e:
            print(f"✗ Error updating store record: {e}")
            return False


def main():
    parser = argparse.ArgumentParser(description="Setup Airbyte for Shopify store")
    parser.add_argument("--store-id", type=int, required=True, help="Store ID in database")
    parser.add_argument("--shop-domain", required=True, help="Shopify shop domain")
    parser.add_argument("--access-token", required=True, help="Shopify access token")
    parser.add_argument("--shop-name", help="Shop name (defaults to domain without .myshopify.com)")

    args = parser.parse_args()

    # Extract shop name from domain if not provided
    shop_name = args.shop_name or args.shop_domain.replace('.myshopify.com', '')

    print(f"🚀 Setting up Airbyte for Store {args.store_id} ({shop_name})")
    print("=" * 60)

    setup = AirbyteSetup()

    # Wait for Airbyte to be ready
    if not setup.wait_for_airbyte():
        print("❌ Failed to connect to Airbyte server. Please ensure Docker services are running.")
        sys.exit(1)

    # Create workspace
    workspace_id = setup.create_workspace()
    if not workspace_id:
        sys.exit(1)

    # Create source
    source_id = setup.create_shopify_source(args.shop_domain, args.access_token, shop_name)
    if not source_id:
        sys.exit(1)

    # Create destination
    dest_id = setup.create_postgres_destination(shop_name)
    if not dest_id:
        sys.exit(1)

    # Discover schema
    catalog = setup.discover_source_schema(source_id)
    if not catalog:
        sys.exit(1)

    # Create connection
    conn_id = setup.create_connection(source_id, dest_id, catalog, shop_name)
    if not conn_id:
        sys.exit(1)

    # Update store record
    if not setup.update_store_record(args.store_id, source_id, dest_id, conn_id):
        sys.exit(1)

    print("\n" + "=" * 60)
    print(f"🎉 Airbyte setup complete for Store {args.store_id}!")
    print("=" * 60)
    print(f"Workspace ID: {workspace_id}")
    print(f"Source ID: {source_id}")
    print(f"Destination ID: {dest_id}")
    print(f"Connection ID: {conn_id}")
    print(f"Airbyte UI: http://localhost:8080")
    print(f"\nYou can now trigger syncs for store {args.store_id}!")
    print("=" * 60)


if __name__ == "__main__":
    main()