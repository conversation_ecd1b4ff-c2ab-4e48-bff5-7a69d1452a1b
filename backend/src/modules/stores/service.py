from typing import List, Optional, Dict, Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from core.services.base_service import BaseService
from modules.stores.models import Store
from modules.stores.schemas import StoreCreate, StoreUpdate


class StoreService(BaseService[Store, StoreCreate, StoreUpdate]):
    """Service for store operations."""

    def __init__(self):
        super().__init__(Store)

    async def get_by_owner(self, db: AsyncSession, owner_id: int) -> List[Store]:
        """Get all stores owned by a user."""
        result = await db.execute(select(Store).where(Store.owner_id == owner_id))
        return result.scalars().all()

    async def get_by_platform(self, db: AsyncSession, platform: str) -> List[Store]:
        """Get all stores by platform."""
        result = await db.execute(select(Store).where(Store.platform == platform))
        return result.scalars().all()

    async def get_active_stores(self, db: AsyncSession) -> List[Store]:
        """Get all active stores."""
        result = await db.execute(select(Store).where(Store.is_active == True))
        return result.scalars().all()

    async def create_store(self, db: AsyncSession, store_create: StoreCreate, owner_id: int) -> Store:
        """Create a new store and automatically set up Airbyte infrastructure."""
        store_data = store_create.model_dump()
        store_data["owner_id"] = owner_id

        db_store = Store(**store_data)
        db.add(db_store)
        await db.commit()
        await db.refresh(db_store)

        # Automatically set up Airbyte infrastructure for the new store
        if db_store.platform.lower() == "shopify" and db_store.admin_access_token and db_store.shop_domain:
            try:
                from core.services.airbyte_service import AirbyteService
                airbyte_service = AirbyteService()

                # Convert async session to sync session for Airbyte setup
                from sqlalchemy.orm import Session
                sync_db = Session(db.bind)

                success = await airbyte_service.setup_shop_sync(
                    sync_db,
                    db_store,
                    db_store.admin_access_token
                )

                if success:
                    # Refresh the store to get updated Airbyte fields
                    await db.refresh(db_store)
                    print(f"Successfully set up Airbyte sync for store {db_store.shop_domain}")
                else:
                    print(f"Failed to set up Airbyte sync for store {db_store.shop_domain}")

                sync_db.close()

            except Exception as e:
                print(f"Error setting up Airbyte for store {db_store.shop_domain}: {e}")
                # Don't fail store creation if Airbyte setup fails
                pass

        return db_store

    async def test_connection(self, store: Store) -> Dict[str, Any]:
        """Test connection to a store's platform by forwarding to the appropriate plugin."""
        if store.platform.lower() == "shopify":
            from plugins.shopify.shopify_service import ShopifyGraphQLService
            from modules.stores.sync_service import StoreSyncService

            store_service = ShopifyGraphQLService(
                shop_domain=store.shop_domain,
                admin_access_token=store.admin_access_token,
                storefront_access_token=store.storefront_access_token
            )

            sync_service = StoreSyncService(store_service, store.shop_domain)
            return await sync_service.test_connection()
        else:
            return {
                "success": False,
                "message": f"Connection testing not implemented for platform: {store.platform}",
                "store_info": None
            }


# Create service instance
store_service = StoreService()
