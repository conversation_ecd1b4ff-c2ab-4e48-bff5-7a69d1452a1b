"""
Sync Module API Router.
Handles data synchronization endpoints using Airbyte connectors.
"""

import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from core.db.database import get_db
from modules.auth.models import User
from modules.auth.router import get_current_user
from modules.stores.models import Store
from modules.sync.service import sync_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/sync/{store_id}")
async def sync_store_data(
    store_id: int,
    sync_types: List[str] = ["products", "orders", "customers"],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Sync store data using Airbyte connectors.

    Args:
        store_id: Store ID to sync
        sync_types: Types of data to sync (products, orders, customers)

    Returns:
        Sync results
    """
    try:
        # Validate store ownership
        store = await db.execute(select(Store).filter(
            Store.id == store_id,
            Store.owner_id == current_user.id
        )).scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Store not found or access denied"
            )

        # Trigger sync
        results = await sync_service.sync_store_data(store, sync_types)

        return {
            "message": "Data sync initiated",
            "results": results
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to sync store data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to sync data: {str(e)}"
        )


@router.get("/sync/{store_id}/status")
async def get_sync_status(
    store_id: int,
    sync_type: str = "products",
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get sync status for a store.

    Args:
        store_id: Store ID
        sync_type: Type of sync to check

    Returns:
        Sync status information
    """
    try:
        # Validate store ownership
        store = await db.execute(select(Store).filter(
            Store.id == store_id,
            Store.owner_id == current_user.id
        )).scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Store not found or access denied"
            )

        # Get sync status
        status_info = await sync_service.get_sync_status(store, sync_type)

        return status_info

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to get sync status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get sync status: {str(e)}"
        )


@router.post("/sync/{store_id}/incremental")
async def trigger_incremental_sync(
    store_id: int,
    sync_type: str = "products",
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Trigger incremental sync for a store.

    Args:
        store_id: Store ID
        sync_type: Type of sync to trigger

    Returns:
        Incremental sync results
    """
    try:
        # Validate store ownership
        store = await db.execute(select(Store).filter(
            Store.id == store_id,
            Store.owner_id == current_user.id
        )).scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Store not found or access denied"
            )

        # Trigger incremental sync
        results = await sync_service.trigger_incremental_sync(store, sync_type)

        return {
            "message": "Incremental sync triggered",
            "results": results
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to trigger incremental sync: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to trigger incremental sync: {str(e)}"
        )


@router.get("/airbyte/platforms")
async def get_supported_platforms():
    """
    Get list of platforms supported by Airbyte integration.

    Returns:
        List of supported platforms
    """
    return {
        "platforms": sync_service.get_supported_platforms(),
        "description": "Platforms supported for data synchronization"
    }