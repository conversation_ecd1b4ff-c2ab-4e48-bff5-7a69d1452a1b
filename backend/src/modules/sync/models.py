"""
Database models for Airbyte sync system.
"""

from datetime import datetime, timezone
from typing import Dict, Any, Optional

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime, Float,
    ForeignKey, JSON, BigInteger, UniqueConstraint, Index
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB

from core.db.database import Base


class WebhookEvent(Base):
    """Webhook events received from Shopify."""

    __tablename__ = "webhook_events"

    id = Column(Integer, primary_key=True, index=True)
    event_id = Column(String(255), nullable=False, unique=True, index=True)
    topic = Column(String(100), nullable=False, index=True)
    shop_domain = Column(String(255), nullable=False, index=True)
    store_id = Column(Integer, ForeignKey("stores.id", ondelete="CASCADE"), nullable=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id", ondelete="SET NULL"), nullable=True)
    event_type = Column(String(50), nullable=True)  # For admin service compatibility
    payload = Column(JSONB, nullable=False)
    headers = Column(JSONB, nullable=True)
    hmac_verified = Column(Boolean, default=False, nullable=True)
    status = Column(String(20), default='pending', nullable=True, index=True)
    retry_count = Column(Integer, default=0, nullable=True)
    max_retries = Column(Integer, default=3, nullable=True)
    last_error = Column(Text, nullable=True)
    processing_started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=True)
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=True)

    # Relationships
    store = relationship("Store", back_populates="webhook_events")

    # Indexes
    __table_args__ = (
        Index('idx_webhook_events_shop_topic', 'shop_domain', 'topic'),
        Index('idx_webhook_events_created_at', 'created_at'),
    )


class SyncCheckpoint(Base):
    """Sync checkpoints for tracking sync progress per store and entity."""

    __tablename__ = "sync_checkpoints"

    id = Column(Integer, primary_key=True, index=True)
    store_id = Column(Integer, ForeignKey("stores.id", ondelete="CASCADE"), nullable=False)
    entity_type = Column(String(50), nullable=False)  # 'products', 'variants', 'images'
    last_updated_at = Column(DateTime(timezone=True), nullable=True)
    last_id = Column(String(255), nullable=True)
    airbyte_state = Column(JSONB, nullable=True)  # Store Airbyte connector state
    total_records = Column(Integer, default=0, nullable=True)
    last_sync_status = Column(String(20), default='pending', nullable=True)
    last_error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=True)
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=True)
    last_successful_sync_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    store = relationship("Store", back_populates="sync_checkpoints")

    # Constraints
    __table_args__ = (
        UniqueConstraint('store_id', 'entity_type', name='uq_sync_checkpoints_store_entity'),
        Index('idx_sync_checkpoints_store_entity', 'store_id', 'entity_type'),
        Index('idx_sync_checkpoints_updated_at', 'last_updated_at'),
    )


class SyncJob(Base):
    """Sync jobs for tracking Airbyte sync operations."""

    __tablename__ = "sync_jobs"

    id = Column(Integer, primary_key=True, index=True)
    store_id = Column(Integer, ForeignKey("stores.id", ondelete="CASCADE"), nullable=False)
    entity_type = Column(String(50), nullable=False)  # 'products', 'variants', 'images'
    job_type = Column(String(20), default='incremental', nullable=False)  # 'incremental', 'full_refresh'
    status = Column(String(20), default='pending', nullable=False, index=True)
    triggered_by = Column(String(50), nullable=True)  # 'webhook', 'manual', 'schedule'
    airbyte_job_id = Column(BigInteger, nullable=True, index=True)
    airbyte_connection_id = Column(String(255), nullable=True)
    started_at = Column(DateTime(timezone=True), nullable=True)
    finished_at = Column(DateTime(timezone=True), nullable=True)
    duration_seconds = Column(Float, nullable=True)
    records_processed = Column(Integer, default=0, nullable=True)
    records_failed = Column(Integer, default=0, nullable=True)
    retry_count = Column(Integer, default=0, nullable=True)
    max_retries = Column(Integer, default=3, nullable=True)
    error_message = Column(Text, nullable=True)
    error_details = Column(JSONB, nullable=True)
    celery_task_id = Column(String(255), nullable=True, index=True)
    job_metadata = Column(JSONB, nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=True)
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=True)

    # Relationships
    store = relationship("Store", back_populates="sync_jobs")

    # Indexes
    __table_args__ = (
        Index('idx_sync_jobs_store_entity', 'store_id', 'entity_type'),
        Index('idx_sync_jobs_created_at', 'created_at'),
    )


class DeadLetterQueue(Base):
    """Dead letter queue for failed operations."""

    __tablename__ = "dead_letter_queue"

    id = Column(Integer, primary_key=True, index=True)
    source_type = Column(String(50), nullable=False, index=True)  # 'webhook', 'sync_job', 'consumer'
    source_id = Column(String(255), nullable=False)
    entity_type = Column(String(50), nullable=True)
    store_id = Column(Integer, ForeignKey("stores.id", ondelete="SET NULL"), nullable=True)
    original_payload = Column(JSONB, nullable=False)
    failure_reason = Column(String(100), nullable=False)
    error_message = Column(Text, nullable=False)
    error_details = Column(JSONB, nullable=True)
    retry_count = Column(Integer, default=0, nullable=True)
    last_retry_at = Column(DateTime(timezone=True), nullable=True)
    resolved = Column(Boolean, default=False, nullable=True, index=True)
    resolved_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=True)
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=True)

    # Relationships
    store = relationship("Store", back_populates="dead_letter_items")

    # Indexes
    __table_args__ = (
        Index('idx_dlq_store_id', 'store_id'),
        Index('idx_dlq_created_at', 'created_at'),
    )

