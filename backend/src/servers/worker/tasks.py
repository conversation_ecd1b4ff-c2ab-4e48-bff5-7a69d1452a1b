"""
Celery tasks for ProductVideo platform.
Defines all background tasks that can be executed asynchronously.
"""

import json
import logging
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List

import redis
import requests
from celery import Task
from celery.exceptions import Retry
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from prometheus_client import Counter, Histogram, Gauge, start_http_server

from .main import celery_app
from servers.worker.processors import (
    MediaGenerationProcessor,
    MediaPushProcessor,
    AnalyticsProcessor,
    AirbyteSyncProcessor
)
from core.db.database import SessionLocal
from core.config import get_settings
from modules.stores.models import Store
from modules.sync.models import <PERSON>hookEvent, SyncJob, DeadLetterQueue
from core.metrics import (
    sync_jobs_total, sync_duration, sync_job_failures, airbyte_api_requests,
    consumer_records_processed
)

logger = logging.getLogger(__name__)

# Initialize settings
settings = get_settings()

# Initialize processors
media_processor = MediaGenerationProcessor()
push_processor = MediaPushProcessor()
analytics_processor = AnalyticsProcessor()
airbyte_sync_processor = AirbyteSyncProcessor()

# Redis client for per-shop locking
redis_client = redis.Redis.from_url(settings.REDIS_URL)

# Additional Prometheus metrics not in core.metrics
active_syncs = Gauge(
    'active_syncs',
    'Number of currently active syncs',
    ['shop_domain']
)

webhook_processing_lag = Histogram(
    'webhook_processing_lag_seconds',
    'Time between webhook receipt and processing',
    ['shop_domain', 'topic']
)


@celery_app.task(name='media_generation.generate_media')
def generate_media(job_data):
    """Generate media (video, image, voice) for products."""
    logger.info(f"Starting media generation task with data: {job_data}")
    try:
        result = media_processor.process(job_data)
        logger.info("Media generation task completed successfully")
        return result
    except Exception as e:
        logger.error(f"Media generation task failed: {e}")
        raise


@celery_app.task(name='media_push.push_to_shopify')
def push_to_shopify(job_data):
    """Push generated media to Shopify store."""
    logger.info(f"Starting Shopify push task with data: {job_data}")
    try:
        result = push_processor.process(job_data)
        logger.info("Shopify push task completed successfully")
        return result
    except Exception as e:
        logger.error(f"Shopify push task failed: {e}")
        raise


@celery_app.task(name='analytics.process_analytics')
def process_analytics(job_data):
    """Process analytics events."""
    logger.info(f"Starting analytics processing task with data: {job_data}")
    try:
        result = analytics_processor.process(job_data)
        logger.info("Analytics processing task completed successfully")
        return result
    except Exception as e:
        logger.error(f"Analytics processing task failed: {e}")
        raise




class ShopLockTask(Task):
    """Base task class with per-shop locking."""

    def acquire_shop_lock(self, shop_id: int, timeout: int = 300) -> bool:
        """
        Acquire per-shop lock to prevent concurrent operations.

        Args:
            shop_id: Store ID
            timeout: Lock timeout in seconds

        Returns:
            True if lock acquired, False otherwise
        """
        lock_key = f"shop_lock:{shop_id}"
        return redis_client.set(lock_key, "locked", nx=True, ex=timeout)

    def release_shop_lock(self, shop_id: int):
        """Release per-shop lock."""
        lock_key = f"shop_lock:{shop_id}"
        redis_client.delete(lock_key)


@celery_app.task(bind=True, base=ShopLockTask, max_retries=3)
async def process_webhook_event(self, webhook_event_id: int):
    """
    Unified webhook processing pipeline that combines:
    1. Business logic processing (from Shopify plugin)
    2. Data synchronization (Airbyte sync)

    Args:
        webhook_event_id: Database ID of webhook event
    """
    db = SessionLocal()

    try:
        # Get webhook event
        webhook_event = db.query(WebhookEvent).filter(
            WebhookEvent.id == webhook_event_id
        ).first()

        if not webhook_event:
            logger.error(f"Webhook event {webhook_event_id} not found")
            return

        # Update processing status
        webhook_event.status = 'processing'
        webhook_event.processing_started_at = datetime.now(timezone.utc)
        db.commit()

        logger.info(
            f"Processing webhook event {webhook_event_id}: "
            f"shop={webhook_event.shop_domain}, topic={webhook_event.topic}"
        )

        # Calculate processing lag for metrics
        received_at = webhook_event.received_at
        if received_at:
            lag_seconds = (datetime.now(timezone.utc) - received_at).total_seconds()
            webhook_processing_lag.labels(
                shop_domain=webhook_event.shop_domain,
                topic=webhook_event.topic
            ).observe(lag_seconds)

        # Acquire shop lock
        if not self.acquire_shop_lock(webhook_event.store_id):
            logger.info(f"Shop {webhook_event.store_id} is locked, retrying...")
            raise self.retry(countdown=30, max_retries=5)

        try:
            # Step 1: Process business logic (from Shopify plugin)
            await airbyte_sync_processor.process_business_logic(webhook_event, db)

            # Step 2: Determine if sync is needed
            entity_type = airbyte_sync_processor.get_entity_type_from_topic(webhook_event.topic)

            if entity_type and airbyte_sync_processor.should_trigger_sync(webhook_event, entity_type, db):
                # Step 3: Trigger Airbyte sync
                sync_task = trigger_airbyte_sync.delay(
                    webhook_event.store_id,
                    entity_type,
                    'webhook',
                    webhook_event_id
                )
                logger.info(
                    f"Triggered sync task {sync_task.id} for webhook {webhook_event_id}"
                )

            # Mark webhook as completed
            webhook_event.status = 'completed'
            webhook_event.completed_at = datetime.now(timezone.utc)
            db.commit()

        finally:
            self.release_shop_lock(webhook_event.store_id)

    except Exception as e:
        logger.error(f"Error processing webhook event {webhook_event_id}: {e}", exc_info=True)

        # Update webhook event with error
        webhook_event.status = 'failed'
        webhook_event.retry_count += 1
        webhook_event.last_error = str(e)

        if webhook_event.retry_count >= webhook_event.max_retries:
            # Move to dead letter queue
            dlq_item = DeadLetterQueue(
                source_type='webhook',
                source_id=str(webhook_event_id),
                entity_type='webhook_event',
                store_id=webhook_event.store_id,
                original_payload=webhook_event.payload,
                failure_reason='max_retries_exceeded',
                error_message=str(e),
                retry_count=webhook_event.retry_count
            )
            db.add(dlq_item)

        db.commit()

        if webhook_event.retry_count < webhook_event.max_retries:
            # Exponential backoff: 2^retry_count * base_delay
            countdown = min(300, 2 ** webhook_event.retry_count * settings.SYNC_BACKOFF_BASE)
            raise self.retry(countdown=countdown)

    finally:
        db.close()




@celery_app.task(bind=True, base=ShopLockTask, max_retries=3)
def trigger_airbyte_sync(
    self,
    store_id: int,
    entity_type: str,
    triggered_by: str = 'manual',
    webhook_event_id: Optional[int] = None
):
    """
    Trigger Airbyte sync job.

    Args:
        store_id: Store ID
        entity_type: Entity type to sync ('products', 'variants', etc.)
        triggered_by: What triggered the sync
        webhook_event_id: Optional webhook event ID
    """
    db = SessionLocal()

    try:
        # Get store
        store = db.query(Store).filter(Store.id == store_id).first()
        if not store or not store.airbyte_connection_id:
            logger.error(f"Store {store_id} not found or missing Airbyte connection")
            return

        # Create sync job record
        sync_job = SyncJob(
            store_id=store_id,
            entity_type=entity_type,
            job_type='incremental',
            status='pending',
            triggered_by=triggered_by,
            airbyte_connection_id=store.airbyte_connection_id,
            celery_task_id=self.request.id,
            job_metadata={'webhook_event_id': webhook_event_id} if webhook_event_id else None
        )
        db.add(sync_job)
        db.commit()
        db.refresh(sync_job)

        logger.info(
            f"Created sync job {sync_job.id} for store {store_id}, entity {entity_type}"
        )

        # Trigger Airbyte sync via API
        airbyte_response = requests.post(
            f"{settings.AIRBYTE_API_URL}/connections/sync",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {settings.AIRBYTE_API_KEY}"
            },
            json={"connectionId": store.airbyte_connection_id},
            timeout=30
        )

        airbyte_api_requests.labels(
            endpoint='connections/sync',
            status=str(airbyte_response.status_code)
        ).inc()

        # Update active syncs metric
        active_syncs.labels(shop_domain=store.shop_domain).inc()

        if airbyte_response.status_code == 429:
            # Rate limited - retry with exponential backoff
            logger.warning(f"Airbyte API rate limited for store {store_id}")
            sync_job.status = 'failed'
            sync_job.error_message = 'Rate limited by Airbyte API'
            db.commit()

            countdown = min(300, 2 ** self.request.retries * settings.SYNC_BACKOFF_BASE)
            raise self.retry(countdown=countdown)

        airbyte_response.raise_for_status()
        airbyte_job_data = airbyte_response.json()

        # Update sync job with Airbyte job ID
        sync_job.airbyte_job_id = airbyte_job_data['job']['id']
        sync_job.status = 'running'
        sync_job.started_at = datetime.now(timezone.utc)
        db.commit()

        logger.info(
            f"Started Airbyte job {sync_job.airbyte_job_id} for sync job {sync_job.id}"
        )

        # Start monitoring task
        monitor_airbyte_job.delay(sync_job.id)

        sync_jobs_total.labels(
            store_domain=store.shop_domain,
            entity_type=entity_type,
            status='started'
        ).inc()

        # Calculate duration if sync completed
        if sync_job.started_at and sync_job.finished_at:
            duration = (sync_job.finished_at - sync_job.started_at).total_seconds()
            sync_duration.labels(
                shop_domain=store.shop_domain,
                entity_type=entity_type
            ).observe(duration)

    except requests.exceptions.RequestException as e:
        logger.error(f"Airbyte API error for store {store_id}: {e}")

        sync_job.status = 'failed'
        sync_job.error_message = f"Airbyte API error: {str(e)}"
        sync_job.finished_at = datetime.now(timezone.utc)
        db.commit()

        sync_job_failures.labels(
            store_domain=store.shop_domain if store else 'unknown',
            entity_type=entity_type,
            failure_reason='airbyte_api_error'
        ).inc()

        # Retry with exponential backoff
        countdown = min(300, 2 ** self.request.retries * settings.SYNC_BACKOFF_BASE)
        raise self.retry(countdown=countdown)

    except Exception as e:
        logger.error(f"Error triggering sync for store {store_id}: {e}", exc_info=True)

        if 'sync_job' in locals():
            sync_job.status = 'failed'
            sync_job.error_message = str(e)
            sync_job.finished_at = datetime.now(timezone.utc)
            db.commit()

        raise

    finally:
        db.close()


@celery_app.task(bind=True, base=ShopLockTask, max_retries=3)
def consumer_upsert(self, store_id: int, entity_type: str, batch_size: int = 100):
    """
    Process staging data and perform transactional upserts to production tables.

    Args:
        store_id: Store ID
        entity_type: Entity type to process ('products', 'variants', etc.)
        batch_size: Number of records to process per batch
    """
    db = SessionLocal()

    try:
        # Get store
        store = db.query(Store).filter(Store.id == store_id).first()
        if not store:
            logger.error(f"Store {store_id} not found")
            return

        logger.info(f"Starting consumer upsert for store {store_id}, entity {entity_type}")

        # Acquire shop lock to prevent concurrent processing
        if not self.acquire_shop_lock(store_id, timeout=600):  # 10 minute timeout
            logger.info(f"Shop {store_id} is locked, retrying consumer upsert...")
            raise self.retry(countdown=60, max_retries=5)

        try:
            if entity_type == 'products':
                processed_count = airbyte_sync_processor.process_airbyte_products(db, store_id, batch_size)
            elif entity_type == 'variants':
                processed_count = airbyte_sync_processor.process_airbyte_variants(db, store_id, batch_size)
            else:
                logger.warning(f"Unknown entity type: {entity_type}")
                return

            logger.info(
                f"Consumer upsert completed for store {store_id}, entity {entity_type}: "
                f"processed {processed_count} records"
            )

            consumer_records_processed.labels(
                store_domain=store.shop_domain,
                entity_type=entity_type
            ).inc(processed_count)

        finally:
            self.release_shop_lock(store_id)

    except Exception as e:
        logger.error(f"Error in consumer upsert for store {store_id}: {e}", exc_info=True)

        # Retry with exponential backoff
        countdown = min(300, 2 ** self.request.retries * settings.SYNC_BACKOFF_BASE)
        raise self.retry(countdown=countdown)

    finally:
        db.close()




@celery_app.task(bind=True, max_retries=10)
def monitor_airbyte_job(self, sync_job_id: int):
    """
    Monitor Airbyte job progress and trigger consumer when complete.

    Args:
        sync_job_id: Database ID of sync job
    """
    db = SessionLocal()

    try:
        # Get sync job
        sync_job = db.query(SyncJob).filter(SyncJob.id == sync_job_id).first()
        if not sync_job or not sync_job.airbyte_job_id:
            logger.error(f"Sync job {sync_job_id} not found or missing Airbyte job ID")
            return

        # Check Airbyte job status
        airbyte_response = requests.get(
            f"{settings.AIRBYTE_API_URL}/jobs/{sync_job.airbyte_job_id}",
            headers={
                "Authorization": f"Bearer {settings.AIRBYTE_API_KEY}"
            },
            timeout=30
        )

        airbyte_api_requests.labels(
            endpoint='jobs/get',
            status=str(airbyte_response.status_code)
        ).inc()

        airbyte_response.raise_for_status()
        job_data = airbyte_response.json()

        job_status = job_data['job']['status']

        # Update sync duration metric on completion
        if job_status in ['succeeded', 'failed', 'cancelled']:
            if sync_job.started_at and sync_job.finished_at:
                duration = (sync_job.finished_at - sync_job.started_at).total_seconds()
                sync_duration.labels(
                    shop_domain=sync_job.store.shop_domain,
                    entity_type=sync_job.entity_type
                ).observe(duration)

        if job_status in ['pending', 'running']:
            # Job still in progress, check again later
            logger.info(f"Airbyte job {sync_job.airbyte_job_id} status: {job_status}")

            # Retry in 30 seconds
            raise self.retry(countdown=30)

        elif job_status == 'succeeded':
            # Job completed successfully
            sync_job.status = 'completed'
            sync_job.finished_at = datetime.now(timezone.utc)

            if sync_job.started_at:
                duration = (sync_job.finished_at - sync_job.started_at).total_seconds()
                sync_job.duration_seconds = duration

                sync_duration.labels(
                    store_domain=sync_job.store.shop_domain,
                    entity_type=sync_job.entity_type
                ).observe(duration)

            db.commit()

            logger.info(f"Airbyte job {sync_job.airbyte_job_id} completed successfully")

            # Trigger consumer to process staging data
            consumer_upsert.delay(sync_job.store_id, sync_job.entity_type)

            sync_jobs_total.labels(
                store_domain=sync_job.store.shop_domain,
                entity_type=sync_job.entity_type,
                status='completed'
            ).inc()

        else:
            # Job failed
            sync_job.status = 'failed'
            sync_job.finished_at = datetime.now(timezone.utc)
            sync_job.error_message = f"Airbyte job failed with status: {job_status}"

            # Extract error details if available
            if 'failureReason' in job_data.get('job', {}):
                sync_job.error_details = {'airbyte_failure_reason': job_data['job']['failureReason']}

            db.commit()

            logger.error(f"Airbyte job {sync_job.airbyte_job_id} failed with status: {job_status}")

            sync_jobs_total.labels(
                store_domain=sync_job.store.shop_domain,
                entity_type=sync_job.entity_type,
                status='failed'
            ).inc()

    except requests.exceptions.RequestException as e:
        logger.error(f"Error checking Airbyte job status: {e}")

        # Retry with exponential backoff
        countdown = min(300, 2 ** self.request.retries * 30)
        raise self.retry(countdown=countdown)

    except Exception as e:
        logger.error(f"Error monitoring sync job {sync_job_id}: {e}", exc_info=True)
        raise

    finally:
        db.close()