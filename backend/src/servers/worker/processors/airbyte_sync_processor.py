import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from sqlalchemy import func, select

from core.db.database import SessionLocal
from modules.sync.models import (
    <PERSON>hookEvent, SyncJob, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DeadLetterQueue
)
from modules.products.models import Product, ProductVariant
from core.metrics import (
    sync_jobs_total, sync_duration, consumer_records_processed,
    sync_job_failures, airbyte_api_requests
)

logger = logging.getLogger(__name__)


class AirbyteSyncProcessor:
    def __init__(self):
        pass

    async def process_business_logic(self, webhook_event: WebhookEvent, db: Session):
        """
        Process business logic for webhook events.
        This includes video generation, analytics tracking, etc.

        Args:
            webhook_event: Webhook event to process
            db: Database session
        """
        try:
            # Process business logic based on webhook topic
            await self._process_webhook_business_logic(webhook_event, db)
            logger.info(f"Business logic processed for webhook {webhook_event.id}")

        except Exception as e:
            logger.error(f"Error in business logic processing for webhook {webhook_event.id}: {e}", exc_info=True)
            # Don't raise - we want sync to continue even if business logic fails

    def get_entity_type_from_topic(self, topic: str) -> Optional[str]:
        """
        Determine entity type from webhook topic.

        Args:
            topic: Webhook topic

        Returns:
            Entity type or None if not applicable
        """
        if topic.startswith('products/'):
            return 'products'
        elif topic.startswith('orders/'):
            return 'orders'
        return None

    def should_trigger_sync(self, webhook_event: WebhookEvent, entity_type: str, db: Session) -> bool:
        """
        Determine if sync should be triggered based on recent activity.

        Args:
            webhook_event: Webhook event
            entity_type: Entity type to sync
            db: Database session

        Returns:
            True if sync should be triggered
        """
        from datetime import timedelta
        # Check for recent sync jobs to avoid too frequent syncs
        recent_sync = db.query(SyncJob).filter(
            SyncJob.store_id == webhook_event.store_id,
            SyncJob.entity_type == entity_type,
            SyncJob.status.in_(['pending', 'running']),
            SyncJob.created_at > datetime.now(timezone.utc) - timedelta(minutes=5)
        ).first()

        if recent_sync:
            logger.info(
                f"Recent sync job {recent_sync.id} exists for "
                f"shop {webhook_event.store_id}, entity {entity_type} - skipping"
            )
            return False

        return True

    def process_airbyte_products(self, db: Session, store_id: int, batch_size: int) -> int:
        """
        Process Airbyte normalized products from shared tables and upsert to production table.

        Args:
            db: Database session
            store_id: Store ID
            batch_size: Batch size for processing

        Returns:
            Number of records processed
        """
        processed_count = 0

        while True:
            # Get batch of new/updated products from Airbyte shared table
            # Use raw SQL to query Airbyte's normalized table with store_id filter
            query = f"""
                SELECT * FROM raw.products
                WHERE store_id = {store_id}
                AND _airbyte_normalized_at > COALESCE(
                    (SELECT last_successful_sync_at FROM sync_checkpoints
                     WHERE store_id = {store_id} AND entity_type = 'products'),
                    '1970-01-01'::timestamp
                )
                ORDER BY _airbyte_normalized_at
                LIMIT {batch_size}
            """

            try:
                result = db.execute(query)
                airbyte_products = result.fetchall()

                if not airbyte_products:
                    break

                logger.info(f"Processing batch of {len(airbyte_products)} products for store {store_id}")

                # Process batch in transaction
                with db.begin():
                    batch_processed = 0
                    last_updated_at = None
                    last_id = None

                    for product_row in airbyte_products:
                        try:
                            # Transform and upsert product
                            self.upsert_product_from_airbyte(db, product_row, store_id)

                            batch_processed += 1
                            last_updated_at = product_row._airbyte_normalized_at
                            last_id = product_row.id

                        except Exception as e:
                            logger.error(
                                f"Error processing Airbyte product {product_row.id}: {e}",
                                exc_info=True
                            )

                    # Update checkpoint after successful batch commit
                    if batch_processed > 0:
                        self.update_sync_checkpoint(
                            db, store_id, 'products', last_updated_at, last_id, batch_processed
                        )

                    processed_count += batch_processed
                    logger.info(f"Processed batch: {batch_processed} products")

            except Exception as e:
                logger.error(f"Error processing product batch for store {store_id}: {e}", exc_info=True)
                break

        return processed_count

    def upsert_product_from_airbyte(self, db: Session, airbyte_product, store_id: int):
        """
        Upsert product from Airbyte normalized table to production table.

        Args:
            db: Database session
            airbyte_product: Airbyte product record (from _airbyte_products)
            store_id: Store ID
        """
        # Extract Shopify ID from admin_graphql_api_id or use id field
        shopify_id = str(airbyte_product.id)
        if hasattr(airbyte_product, 'admin_graphql_api_id') and airbyte_product.admin_graphql_api_id:
            # Extract ID from GraphQL API ID: gid://shopify/Product/123456789
            shopify_id = airbyte_product.admin_graphql_api_id.split('/')[-1]

        # Check if product exists
        existing_product = db.query(Product).filter(
            Product.external_id == shopify_id,
            Product.store_id == store_id
        ).first()

        # Prepare product data from Airbyte record
        product_data = {
            'store_id': store_id,
            'external_id': shopify_id,
            'title': getattr(airbyte_product, 'title', '') or '',
            'description': getattr(airbyte_product, 'body_html', '') or '',
            'handle': getattr(airbyte_product, 'handle', '') or '',
            'vendor': getattr(airbyte_product, 'vendor', '') or '',
            'product_type': getattr(airbyte_product, 'product_type', '') or '',
            'status': getattr(airbyte_product, 'status', 'active') or 'active',
            'tags': getattr(airbyte_product, 'tags', '') or '',
            'published': getattr(airbyte_product, 'published_at', None) is not None,
            'seo_title': getattr(airbyte_product, 'seo_title', None),
            'seo_description': getattr(airbyte_product, 'seo_description', None),
            'created_at': getattr(airbyte_product, 'created_at', None),
            'updated_at': getattr(airbyte_product, 'updated_at', datetime.now(timezone.utc)) or datetime.now(timezone.utc)
        }

        if existing_product:
            # Use tie-breaker: only update if Airbyte data is newer
            airbyte_updated = getattr(airbyte_product, 'updated_at', None)
            if (airbyte_updated and
                existing_product.updated_at and
                airbyte_updated <= existing_product.updated_at):
                # Airbyte data is older, skip update
                logger.debug(f"Skipping product {shopify_id} - Airbyte data is older")
                return

            # Update existing product
            for key, value in product_data.items():
                if key not in ['store_id', 'external_id']:  # Don't update these fields
                    setattr(existing_product, key, value)

            existing_product.updated_at = datetime.now(timezone.utc)

        else:
            # Create new product
            new_product = Product(**product_data)
            db.add(new_product)

    def process_airbyte_variants(self, db: Session, store_id: int, batch_size: int) -> int:
        """
        Process Airbyte normalized product variants from shared tables and upsert to production table.

        Args:
            db: Database session
            store_id: Store ID
            batch_size: Batch size for processing

        Returns:
            Number of records processed
        """
        processed_count = 0

        while True:
            # Get batch of new/updated variants from Airbyte shared table
            query = f"""
                SELECT * FROM raw.product_variants
                WHERE store_id = {store_id}
                AND _airbyte_normalized_at > COALESCE(
                    (SELECT last_successful_sync_at FROM sync_checkpoints
                     WHERE store_id = {store_id} AND entity_type = 'variants'),
                    '1970-01-01'::timestamp
                )
                ORDER BY _airbyte_normalized_at
                LIMIT {batch_size}
            """

            try:
                result = db.execute(query)
                airbyte_variants = result.fetchall()

                if not airbyte_variants:
                    break

                logger.info(f"Processing batch of {len(airbyte_variants)} variants for store {store_id}")

                # Process batch in transaction
                with db.begin():
                    batch_processed = 0
                    last_updated_at = None
                    last_id = None

                    for variant_row in airbyte_variants:
                        try:
                            # Transform and upsert variant
                            self.upsert_variant_from_airbyte(db, variant_row, store_id)

                            batch_processed += 1
                            last_updated_at = variant_row._airbyte_normalized_at
                            last_id = variant_row.id

                        except Exception as e:
                            logger.error(
                                f"Error processing Airbyte variant {variant_row.id}: {e}",
                                exc_info=True
                            )

                    # Update checkpoint after successful batch commit
                    if batch_processed > 0:
                        self.update_sync_checkpoint(
                            db, store_id, 'variants', last_updated_at, last_id, batch_processed
                        )

                    processed_count += batch_processed
                    logger.info(f"Processed batch: {batch_processed} variants")

            except Exception as e:
                logger.error(f"Error processing variant batch for store {store_id}: {e}", exc_info=True)
                break

        return processed_count

    def upsert_variant_from_airbyte(self, db: Session, airbyte_variant, store_id: int):
        """
        Upsert product variant from Airbyte normalized table to production table.

        Args:
            db: Database session
            airbyte_variant: Airbyte variant record (from _airbyte_product_variants)
            store_id: Store ID
        """
        # Extract Shopify ID
        shopify_id = str(airbyte_variant.id)
        if hasattr(airbyte_variant, 'admin_graphql_api_id') and airbyte_variant.admin_graphql_api_id:
            shopify_id = airbyte_variant.admin_graphql_api_id.split('/')[-1]

        # Get product ID
        product_shopify_id = str(airbyte_variant.product_id)
        product = db.query(Product).filter(
            Product.external_id == product_shopify_id,
            Product.store_id == store_id
        ).first()

        if not product:
            logger.warning(f"Product {product_shopify_id} not found for variant {shopify_id}")
            return

        # Check if variant exists
        existing_variant = db.query(ProductVariant).filter(
            ProductVariant.external_id == shopify_id,
            ProductVariant.product_id == product.id
        ).first()

        # Prepare variant data from Airbyte record
        variant_data = {
            'product_id': product.id,
            'external_id': shopify_id,
            'title': getattr(airbyte_variant, 'title', '') or '',
            'price': float(getattr(airbyte_variant, 'price', 0)) if getattr(airbyte_variant, 'price', None) else 0.0,
            'compare_at_price': float(getattr(airbyte_variant, 'compare_at_price', 0)) if getattr(airbyte_variant, 'compare_at_price', None) else None,
            'sku': getattr(airbyte_variant, 'sku', '') or '',
            'barcode': getattr(airbyte_variant, 'barcode', '') or '',
            'position': getattr(airbyte_variant, 'position', 1) or 1,
            'inventory_quantity': getattr(airbyte_variant, 'inventory_quantity', 0) or 0,
            'weight': getattr(airbyte_variant, 'weight', None),
            'weight_unit': getattr(airbyte_variant, 'weight_unit', 'kg') or 'kg',
            'requires_shipping': getattr(airbyte_variant, 'requires_shipping', True) or True,
            'taxable': getattr(airbyte_variant, 'taxable', True) or True,
            'option1': getattr(airbyte_variant, 'option1', None),
            'option2': getattr(airbyte_variant, 'option2', None),
            'option3': getattr(airbyte_variant, 'option3', None),
            'created_at': getattr(airbyte_variant, 'created_at', None),
            'updated_at': getattr(airbyte_variant, 'updated_at', datetime.now(timezone.utc)) or datetime.now(timezone.utc)
        }

        if existing_variant:
            # Use tie-breaker: only update if Airbyte data is newer
            airbyte_updated = getattr(airbyte_variant, 'updated_at', None)
            if (airbyte_updated and
                existing_variant.updated_at and
                airbyte_updated <= existing_variant.updated_at):
                logger.debug(f"Skipping variant {shopify_id} - Airbyte data is older")
                return

            # Update existing variant
            for key, value in variant_data.items():
                if key not in ['product_id', 'external_id']:
                    setattr(existing_variant, key, value)

            existing_variant.updated_at = datetime.now(timezone.utc)

        else:
            # Create new variant
            new_variant = ProductVariant(**variant_data)
            db.add(new_variant)

    def update_sync_checkpoint(
        self,
        db: Session,
        store_id: int,
        entity_type: str,
        last_updated_at: Optional[datetime],
        last_id: Optional[str],
        records_processed: int
    ):
        """
        Update sync checkpoint after successful batch processing.

        Args:
            db: Database session
            store_id: Store ID
            entity_type: Entity type
            last_updated_at: Last updated timestamp
            last_id: Last processed ID
            records_processed: Number of records processed
        """
        checkpoint = db.query(SyncCheckpoint).filter(
            SyncCheckpoint.store_id == store_id,
            SyncCheckpoint.entity_type == entity_type
        ).first()

        if checkpoint:
            checkpoint.last_updated_at = last_updated_at
            checkpoint.last_id = last_id
            checkpoint.total_records += records_processed
            checkpoint.last_sync_status = 'completed'
            checkpoint.last_successful_sync_at = datetime.now(timezone.utc)
            checkpoint.updated_at = datetime.now(timezone.utc)
        else:
            checkpoint = SyncCheckpoint(
                store_id=store_id,
                entity_type=entity_type,
                last_updated_at=last_updated_at,
                last_id=last_id,
                total_records=records_processed,
                last_sync_status='completed',
                last_successful_sync_at=datetime.now(timezone.utc)
            )
            db.add(checkpoint)

    async def _process_webhook_business_logic(self, webhook_event, db):
        """Process business logic for webhooks in Airbyte architecture."""
        try:
            topic = webhook_event.topic

            if topic == "products/create":
                # Trigger video generation for new products
                await self._trigger_video_generation_for_product(webhook_event.payload.get("id"), db)
            elif topic == "products/update":
                # Check if significant changes warrant video regeneration
                if self._should_regenerate_video(webhook_event.payload):
                    await self._trigger_video_regeneration_for_product(webhook_event.payload.get("id"), db)
            elif topic == "products/delete":
                # Clean up associated resources
                await self._cleanup_product_resources(webhook_event.payload.get("id"), db)
            elif topic == "orders/create":
                # Track order conversion for analytics
                await self._track_order_conversion(webhook_event.payload, db)

            logger.info(f"Processed business logic for {topic}")

        except Exception as e:
            logger.error(f"Error in webhook business logic: {e}", exc_info=True)

    async def _trigger_video_generation_for_product(self, product_id: str, db):
        """Trigger video generation for a product."""
        try:
            from modules.media_generation.service import media_generation_service
            from modules.media_generation.schemas import MediaGenerateRequest

            # Create video generation request
            request = MediaGenerateRequest(
                shop_id=1,  # Will be set from webhook context
                product_ids=[product_id],
                media_type="video",
                template_id="default",
                voice_id="default",
                aspect_ratio="16:9",
                text_input=f"Product {product_id} - Amazing features and benefits"
            )

            # Create generation jobs
            jobs = await media_generation_service.create_generation_jobs(
                db=db,
                user_id=1,  # Default user for now
                request=request
            )

            logger.info(f"Triggered video generation for product {product_id}: {len(jobs)} jobs created")

        except Exception as e:
            logger.error(f"Error triggering video generation for product {product_id}: {e}", exc_info=True)

    def _should_regenerate_video(self, payload: dict) -> bool:
        """Determine if product changes warrant video regeneration."""
        significant_fields = ['title', 'body_html', 'images', 'variants']
        updated_fields = payload.get('updated_fields', [])

        for field in significant_fields:
            if field in updated_fields:
                return True

        if 'images' in payload:
            return True

        return False

    async def _trigger_video_regeneration_for_product(self, product_id: str, db):
        """Trigger video regeneration for an updated product."""
        try:
            from modules.media_generation.service import media_generation_service

            # Find existing video variants for this product
            from modules.media_generation.models import MediaVariant

            result = await db.execute(
                select(MediaVariant).where(MediaVariant.product_id == product_id)
            )
            variants = result.scalars().all()

            if variants:
                # Regenerate the first variant
                await media_generation_service.regenerate_variant(
                    job_id=variants[0].job_id,
                    variant_id=variants[0].id,
                    override_params={"reason": "product_update"}
                )
                logger.info(f"Triggered video regeneration for product {product_id}")
            else:
                # No existing variants, trigger new generation
                await self._trigger_video_generation_for_product(product_id, db)

        except Exception as e:
            logger.error(f"Error triggering video regeneration for product {product_id}: {e}", exc_info=True)

    async def _cleanup_product_resources(self, product_id: str, db):
        """Clean up resources associated with a deleted product."""
        try:
            from modules.media_generation.models import MediaVariant, MediaJob

            # Find all variants for this product
            result = await db.execute(
                select(MediaVariant).where(MediaVariant.product_id == product_id)
            )
            variants = result.scalars().all()

            deleted_variants = 0
            deleted_jobs = 0

            for variant in variants:
                # Delete the variant
                await db.delete(variant)
                deleted_variants += 1

                # Check if job has no more variants
                job_result = await db.execute(
                    select(MediaJob).where(MediaJob.id == variant.job_id)
                )
                job = job_result.scalar_one_or_none()

                if job:
                    variants_count_result = await db.execute(
                        select(func.count(MediaVariant.id)).where(MediaVariant.job_id == job.id)
                    )
                    remaining_variants = variants_count_result.scalar()

                    if remaining_variants == 0:
                        await db.delete(job)
                        deleted_jobs += 1

            await db.commit()
            logger.info(f"Cleaned up product {product_id}: {deleted_variants} variants, {deleted_jobs} jobs")

        except Exception as e:
            logger.error(f"Error cleaning up product resources for {product_id}: {e}", exc_info=True)

    async def _track_order_conversion(self, payload: dict, db):
        """Track order conversion for analytics."""
        try:
            from modules.analytics.event_service import analytics_event_service
            from modules.analytics.event_models import EventType

            order_id = str(payload["id"])
            order_value = float(payload.get("total_price", 0))

            # Extract product IDs from line items
            product_ids = []
            for line_item in payload.get("line_items", []):
                if line_item.get("product_id"):
                    product_ids.append(str(line_item["product_id"]))

            # Create purchase events for each product
            for product_id in product_ids:
                event_request = {
                    "event_type": EventType.PURCHASE,
                    "session_id": f"order_{order_id}",
                    "user_id": payload.get("customer", {}).get("id"),
                    "product_id": product_id,
                    "order_id": order_id,
                    "order_value": order_value,
                    "currency": payload.get("currency", "USD"),
                    "timestamp": datetime.utcnow(),
                    "properties": {
                        "order_number": payload.get("order_number"),
                        "line_items_count": len(payload.get("line_items", []))
                    }
                }

                # Ingest the event
                await analytics_event_service.ingest_event(
                    db=db,
                    tenant_id=1,  # Default tenant
                    event_request=event_request
                )

            logger.info(f"Tracked conversion for order {order_id} with {len(product_ids)} products")

        except Exception as e:
            logger.error(f"Error tracking conversion for order {order_id}: {e}", exc_info=True)