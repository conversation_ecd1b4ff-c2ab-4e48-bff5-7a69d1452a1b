-- Database initialization for E-commerce platform
-- This script sets up the necessary schemas and permissions

-- Create airbyte database and user for Airbyte services
CREATE DATABASE airbyte;
CREATE USER docker WITH PASSWORD 'dev_password';
GRANT ALL PRIVILEGES ON DATABASE airbyte TO docker;

-- Create temporal database for Temporal workflow service
CREATE DATABASE temporal;
CREATE DATABASE temporal_visibility;
GRANT ALL PRIVILEGES ON DATABASE temporal TO app_user;
GRANT ALL PRIVILEGES ON DATABASE temporal_visibility TO app_user;

-- Connect to main database
\c ecommerce_db;

-- Create staging schemas for data processing
CREATE SCHEMA IF NOT EXISTS staging;
CREATE SCHEMA IF NOT EXISTS raw;

-- Grant permissions on schemas
GRANT ALL ON SCHEMA staging TO app_user;
GRANT ALL ON SCHEMA raw TO app_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA staging GRANT ALL ON TABLES TO app_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA staging GRANT ALL ON SEQUENCES TO app_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA raw GRANT ALL ON TABLES TO app_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA raw GRANT ALL ON SEQUENCES TO app_user;
