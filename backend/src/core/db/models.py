# This file is used to import all SQLAlchemy models to ensure they are registered
# with the Base.metadata object. This helps prevent issues with relationships
# not being properly configured during application startup.

# Import all module models here
from modules.products import models as products_models
from modules.stores import models as stores_models
from modules.orders import models as orders_models
from modules.media_generation import models as media_generation_models
from modules.billing import models as billing_models
from modules.analytics import event_models as analytics_event_models
from modules.customers import models as customers_models
from modules.sync import models as sync_models

# Auth module models - using auth User model instead of users User model
from modules.auth import models as auth_models

