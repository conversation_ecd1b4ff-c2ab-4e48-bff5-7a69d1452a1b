"""
Airbyte Service
Handles Airbyte source, destination, and connection management for e-commerce platforms.
"""

import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from pathlib import Path

import requests
import psycopg2
from sqlalchemy.orm import Session

from src.core.config import settings
from src.modules.stores.models import Store
from src.core.services.redis_lock import redis_lock

logger = logging.getLogger(__name__)


class AirbyteService:
    """Manages Airbyte sources, destinations, and connections for e-commerce platforms."""

    def __init__(self):
        self.api_url = settings.AIRBYTE_API_URL
        self.api_key = settings.AIRBYTE_API_KEY
        self.workspace_id = settings.AIRBYTE_WORKSPACE_ID

        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        # Validate configuration on initialization
        self._validate_configuration()

    def _validate_configuration(self):
        """Validate Airbyte configuration and connectivity."""
        logger.info("Validating Airbyte configuration...")
        logger.info(f"API URL: {self.api_url}")
        logger.info(f"Workspace ID: {self.workspace_id}")
        logger.info(f"API Key configured: {'Yes' if self.api_key else 'No'}")

        # Validate database connectivity
        self._validate_database_connectivity()

        # Test basic connectivity
        try:
            response = requests.get(f"{self.api_url}/health", timeout=10)
            if response.status_code == 200:
                logger.info("Airbyte server health check passed")
            else:
                logger.warning(f"Airbyte server health check failed: {response.status_code}")
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to connect to Airbyte server: {e}")

        # Check workspace if configured
        if self.workspace_id:
            self._validate_workspace()

    def _validate_database_connectivity(self):
        """Validate database connectivity for Airbyte."""
        try:
            conn = psycopg2.connect(
                host=settings.AIRBYTE_DATABASE_HOST,
                port=settings.AIRBYTE_DATABASE_PORT,
                user=settings.AIRBYTE_DATABASE_USER,
                password=settings.AIRBYTE_DATABASE_PASSWORD,
                database=settings.AIRBYTE_DATABASE_NAME
            )
            conn.close()
            logger.info(f"Airbyte database connectivity test passed: {settings.AIRBYTE_DATABASE_NAME}")
        except psycopg2.Error as e:
            logger.error(f"Airbyte database connectivity test failed: {e}")
            logger.error(f"Database config: host={settings.AIRBYTE_DATABASE_HOST}, port={settings.AIRBYTE_DATABASE_PORT}, user={settings.AIRBYTE_DATABASE_USER}, db={settings.AIRBYTE_DATABASE_NAME}")

    def _validate_workspace(self):
        """Validate workspace exists and is accessible."""
        try:
            response = requests.get(
                f"{self.api_url}/workspaces/{self.workspace_id}",
                headers=self.headers,
                timeout=10
            )
            if response.status_code == 200:
                workspace_data = response.json()
                logger.info(f"Workspace validation successful: {workspace_data.get('name', 'Unknown')}")
            else:
                logger.warning(f"Workspace validation failed: {response.status_code} - {response.text}")
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to validate workspace: {e}")

    def create_shopify_source(
        self,
        shop_domain: str,
        access_token: str,
        shop_name: str,
        start_date: str = None
    ) -> Optional[str]:
        """
        Create Shopify source in Airbyte.

        Args:
            shop_domain: Shopify shop domain
            access_token: Shopify access token
            shop_name: Shop name for display
            start_date: Sync start date (ISO format)

        Returns:
            Source ID if successful, None otherwise
        """
        if not start_date:
            start_date = datetime.now(timezone.utc).strftime("%Y-%m-%d")

        # Load source template
        template_path = Path(__file__).parent.parent / "configs" / "airbyte_source_shopify.json"
        with open(template_path, 'r') as f:
            source_config = json.load(f)

        # Replace placeholders
        source_config["workspaceId"] = self.workspace_id
        source_config["connectionConfiguration"]["shop"] = shop_domain
        source_config["connectionConfiguration"]["credentials"]["api_password"] = access_token
        source_config["connectionConfiguration"]["start_date"] = start_date
        source_config["name"] = f"Shopify Source - {shop_name}"

        try:
            response = requests.post(
                f"{self.api_url}/sources/create",
                headers=self.headers,
                json=source_config,
                timeout=30
            )
            response.raise_for_status()

            source_data = response.json()
            source_id = source_data["sourceId"]

            logger.info(f"Created Shopify source {source_id} for shop {shop_domain}")
            return source_id

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to create Shopify source for {shop_domain}: {e}")
            return None

    def create_postgres_destination(self, shop_id: int) -> Optional[str]:
        """
        Create PostgreSQL destination in Airbyte.

        Args:
            shop_id: Shop ID for table prefixing

        Returns:
            Destination ID if successful, None otherwise
        """
        destination_config = {
            "destinationDefinitionId": "25c5221d-dce2-4163-ade9-739ef790f503",  # Postgres
            "workspaceId": self.workspace_id,
            "connectionConfiguration": {
                "host": settings.DATABASE_HOST,
                "port": settings.DATABASE_PORT,
                "database": settings.DATABASE_NAME,
                "schema": "public",
                "username": settings.DATABASE_USER,
                "password": settings.DATABASE_PASSWORD,
                "ssl_mode": {"mode": "disable"},
                "tunnel_method": {"tunnel_method": "NO_TUNNEL"},
                "raw_data_schema": "raw",
                "disable_type_dedupe": False
            },
            "name": f"PostgreSQL Destination - Shop {shop_id}",
            "destinationName": "Postgres"
        }

        try:
            response = requests.post(
                f"{self.api_url}/destinations/create",
                headers=self.headers,
                json=destination_config,
                timeout=30
            )
            response.raise_for_status()

            destination_data = response.json()
            destination_id = destination_data["destinationId"]

            logger.info(f"Created PostgreSQL destination {destination_id} for shop {shop_id}")
            return destination_id

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to create PostgreSQL destination for shop {shop_id}: {e}")
            return None

    def discover_source_schema(self, source_id: str) -> Optional[Dict[str, Any]]:
        """
        Discover source schema.

        Args:
            source_id: Source ID

        Returns:
            Source catalog if successful, None otherwise
        """
        try:
            response = requests.post(
                f"{self.api_url}/sources/discover_schema",
                headers=self.headers,
                json={"sourceId": source_id},
                timeout=60
            )
            response.raise_for_status()

            catalog_data = response.json()
            return catalog_data["catalog"]

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to discover schema for source {source_id}: {e}")
            return None

    def create_connection(
        self,
        source_id: str,
        destination_id: str,
        shop_id: int,
        shop_name: str,
        catalog: Dict[str, Any]
    ) -> Optional[str]:
        """
        Create connection between source and destination with scalable table design.

        Args:
            source_id: Source ID
            destination_id: Destination ID
            shop_id: Shop ID
            shop_name: Shop name
            catalog: Source catalog

        Returns:
            Connection ID if successful, None otherwise
        """
        # Load connection template
        template_path = Path(__file__).parent.parent / "configs" / "airbyte_connection_products.json"
        with open(template_path, 'r') as f:
            connection_config = json.load(f)

        # Replace placeholders - Remove prefix for scalable shared tables
        connection_config["name"] = f"Shopify Products Sync - {shop_name}"
        connection_config["prefix"] = ""  # No prefix - use shared tables
        connection_config["sourceId"] = source_id
        connection_config["destinationId"] = destination_id

        # Modify sync catalog to include store_id in primary keys and add store_id column
        modified_catalog = self._modify_catalog_for_multi_tenant(catalog, shop_id)
        connection_config["syncCatalog"] = modified_catalog

        try:
            response = requests.post(
                f"{self.api_url}/connections/create",
                headers=self.headers,
                json=connection_config,
                timeout=30
            )
            response.raise_for_status()

            connection_data = response.json()
            connection_id = connection_data["connectionId"]

            logger.info(f"Created scalable connection {connection_id} for shop {shop_id}")
            return connection_id

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to create connection for shop {shop_id}: {e}")
            return None

    def _modify_catalog_for_multi_tenant(self, catalog: Dict[str, Any], shop_id: int) -> Dict[str, Any]:
        """
        Modify the sync catalog to support multi-tenant shared tables.

        Args:
            catalog: Original Airbyte catalog
            shop_id: Shop ID to add to primary keys

        Returns:
            Modified catalog with store_id in primary keys
        """
        modified_catalog = catalog.copy()

        for stream in modified_catalog.get("streams", []):
            config = stream.get("config", {})

            # Add store_id to primary key for deduplication
            primary_key = config.get("primaryKey", [["id"]])
            if not any("store_id" in str(pk) for pk in primary_key):
                # Add store_id as first element in compound primary key
                primary_key.insert(0, ["store_id"])
                config["primaryKey"] = primary_key

            # Ensure we're using append_dedup mode for proper deduplication
            config["destinationSyncMode"] = "append_dedup"

        return modified_catalog

    async def setup_shop_sync(
        self,
        db: Session,
        store: Store,
        access_token: str,
        start_date: str = None
    ) -> bool:
        """
        Set up complete Airbyte sync for a shop with locking.

        Args:
            db: Database session
            store: Store model instance
            access_token: Shopify access token
            start_date: Sync start date

        Returns:
            True if successful, False otherwise
        """
        lock_key = f"lock:shop:{store.id}"

        async with redis_lock.acquire_lock(lock_key) as lock_acquired:
            if not lock_acquired:
                logger.warning(f"Setup already in progress for shop {store.id}")
                return False

            try:
                # Create source
                source_id = self.create_shopify_source(
                    store.shop_domain,
                    access_token,
                    store.shop_name,
                    start_date
                )
                if not source_id:
                    return False

                # Create destination
                destination_id = self.create_postgres_destination(store.id)
                if not destination_id:
                    return False

                # Discover schema
                catalog = self.discover_source_schema(source_id)
                if not catalog:
                    return False

                # Create connection
                connection_id = self.create_connection(
                    source_id,
                    destination_id,
                    store.id,
                    store.shop_name,
                    catalog
                )
                if not connection_id:
                    return False

                # Update store with Airbyte IDs
                store.airbyte_source_id = source_id
                store.airbyte_destination_id = destination_id
                store.airbyte_connection_id = connection_id
                db.commit()

                logger.info(
                    f"Successfully set up Airbyte sync for shop {store.shop_domain}: "
                    f"source={source_id}, destination={destination_id}, connection={connection_id}"
                )

                return True

            except Exception as e:
                logger.error(f"Failed to set up Airbyte sync for shop {store.shop_domain}: {e}")
                db.rollback()
                return False

    async def trigger_sync(self, connection_id: str, shop_id: int) -> Optional[int]:
        """
        Trigger manual sync for a connection with per-shop locking.

        Args:
            connection_id: Connection ID
            shop_id: Shop ID for locking

        Returns:
            Airbyte job ID if successful, None otherwise
        """
        lock_key = f"lock:shop:{shop_id}"

        async with redis_lock.acquire_lock(lock_key) as lock_acquired:
            if not lock_acquired:
                logger.warning(f"Sync already in progress for shop {shop_id}")
                return None

            try:
                response = requests.post(
                    f"{self.api_url}/connections/sync",
                    headers=self.headers,
                    json={"connectionId": connection_id},
                    timeout=30
                )
                response.raise_for_status()

                job_data = response.json()
                job_id = job_data["job"]["id"]

                logger.info(f"Triggered sync for connection {connection_id}, job ID: {job_id}")
                return job_id

            except requests.exceptions.RequestException as e:
                logger.error(f"Failed to trigger sync for connection {connection_id}: {e}")
                return None

    def get_job_status(self, job_id: int) -> Optional[Dict[str, Any]]:
        """
        Get job status from Airbyte.

        Args:
            job_id: Airbyte job ID

        Returns:
            Job data if successful, None otherwise
        """
        try:
            response = requests.get(
                f"{self.api_url}/jobs/{job_id}",
                headers=self.headers,
                timeout=30
            )
            response.raise_for_status()

            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get job status for job {job_id}: {e}")
            return None