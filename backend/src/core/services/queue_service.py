"""
Celery integration for ProductVideo platform.
Replaces BullMQ with Celery for distributed task processing.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum

from celery import Celery
from celery.result import AsyncResult

from core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

# Create Celery app instance
celery_app = Celery(
    'productvideo_worker',
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=['servers.worker.tasks']
)


class TaskPriority(Enum):
    """Task priority levels for Celery."""
    LOW = 1
    NORMAL = 5
    HIGH = 10
    URGENT = 15


class CeleryService:
    """
    Celery service for managing media generation tasks.
    Provides a clean interface over Celery for our specific use cases.
    """

    def __init__(self):
        self.redis_url = settings.REDIS_URL
        logger.info("Initialized Celery service")
    
    def enqueue_media_generation(
        self,
        tenant_id: int,
        job_id: int,
        product_ids: List[str],
        media_type: str,
        template_id: Optional[str] = None,
        voice_id: Optional[str] = None,
        text_input: Optional[str] = None,
        priority: TaskPriority = TaskPriority.NORMAL,
        delay_seconds: int = 0,
        **kwargs
    ) -> str:
        """
        Enqueue a media generation task.

        Args:
            tenant_id: Tenant ID
            job_id: Database job ID
            product_ids: List of product IDs to generate media for
            media_type: Type of media to generate ('video', 'image', 'voice')
            template_id: Template to use (for video/image)
            voice_id: Voice to use (for video/voice)
            text_input: Text input for voice generation
            priority: Task priority
            delay_seconds: Delay before processing (seconds)
            **kwargs: Additional task data

        Returns:
            Celery task ID
        """
        from servers.worker.tasks import generate_media

        task_data = {
            "tenant_id": tenant_id,
            "job_id": job_id,
            "product_ids": product_ids,
            "media_type": media_type,
            "template_id": template_id,
            "voice_id": voice_id,
            "text_input": text_input,
            "created_at": datetime.utcnow().isoformat(),
            **kwargs
        }

        # Convert priority to Celery priority (1-10, higher is more important)
        celery_priority = priority.value

        result = generate_media.apply_async(
            args=[task_data],
            priority=celery_priority,
            countdown=delay_seconds if delay_seconds > 0 else None
        )

        logger.info(f"Enqueued {media_type} generation task {result.id} for tenant {tenant_id}")
        return result.id
    
    def enqueue_media_push(
        self,
        tenant_id: int,
        variant_id: int,
        product_id: str,
        shop_domain: str,
        priority: TaskPriority = TaskPriority.HIGH,
        **kwargs
    ) -> str:
        """
        Enqueue a media push to Store task.

        Args:
            tenant_id: Tenant ID
            variant_id: Media variant ID
            product_id: Store product ID
            shop_domain: Store shop domain
            priority: Task priority
            **kwargs: Additional task data

        Returns:
            Celery task ID
        """
        from servers.worker.tasks import push_to_shopify

        task_data = {
            "tenant_id": tenant_id,
            "variant_id": variant_id,
            "product_id": product_id,
            "shop_domain": shop_domain,
            "created_at": datetime.utcnow().isoformat(),
            **kwargs
        }

        celery_priority = priority.value

        result = push_to_shopify.apply_async(
            args=[task_data],
            priority=celery_priority
        )

        logger.info(f"Enqueued media push task {result.id} for variant {variant_id}")
        return result.id
    
    def enqueue_analytics_processing(
        self,
        tenant_id: int,
        event_data: Dict[str, Any],
        priority: TaskPriority = TaskPriority.LOW,
        **kwargs
    ) -> str:
        """
        Enqueue analytics event processing.

        Args:
            tenant_id: Tenant ID
            event_data: Analytics event data
            priority: Task priority
            **kwargs: Additional task data

        Returns:
            Celery task ID
        """
        from servers.worker.tasks import process_analytics

        task_data = {
            "tenant_id": tenant_id,
            "event_data": event_data,
            "created_at": datetime.utcnow().isoformat(),
            **kwargs
        }

        celery_priority = priority.value

        result = process_analytics.apply_async(
            args=[task_data],
            priority=celery_priority
        )

        logger.info(f"Enqueued analytics processing task {result.id}")
        return result.id

    def enqueue_sync_job(
        self,
        job_data: Dict[str, Any],
        priority: TaskPriority = TaskPriority.NORMAL,
        **kwargs
    ) -> str:
        """
        Enqueue a sync task for store data synchronization using Airbyte.

        Args:
            job_data: Sync task data including store_id, entity_type, triggered_by
            priority: Task priority
            **kwargs: Additional task data

        Returns:
            Celery task ID
        """
        from servers.worker.tasks import trigger_airbyte_sync

        store_id = job_data.get("store_id")
        entity_type = job_data.get("sync_type", "products")  # Map sync_type to entity_type
        triggered_by = job_data.get("triggered_by", "api")

        # Use Airbyte-based sync instead of direct API calls
        result = trigger_airbyte_sync.apply_async(
            args=[store_id, entity_type, triggered_by],
            priority=priority.value
        )

        logger.info(f"Enqueued Airbyte {entity_type} sync task {result.id} for store {store_id}")
        return result.id

    async def can_enqueue_sync(
        self,
        store_id: int,
        sync_type: str,
        cooldown_hours: int = 1
    ) -> tuple[bool, str]:
        """
        Check if a sync job can be enqueued for the given store and type.

        Args:
            store_id: Store ID to check
            sync_type: Type of sync ('products', 'orders', etc.)
            cooldown_hours: Minimum hours between syncs

        Returns:
            tuple: (can_enqueue, reason)
        """
        from core.db.database import get_db_session_factory
        from modules.stores.progress_service import ProgressService

        db_factory = get_db_session_factory()
        db = db_factory()

        try:
            progress_service = ProgressService(db)
            return await progress_service.can_start_sync(store_id, sync_type, cooldown_hours)
        finally:
            await db.close()
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get task status and details.

        Args:
            task_id: Celery task ID

        Returns:
            Task status information
        """
        result = AsyncResult(task_id, app=celery_app)

        if not result:
            return None

        return {
            "id": result.id,
            "state": result.state,
            "info": result.info,
            "result": result.result if result.state == "SUCCESS" else None,
            "traceback": result.traceback if result.state == "FAILURE" else None,
        }

    def get_queue_stats(self) -> Dict[str, Any]:
        """Get statistics for all queues using Celery events."""
        # Celery doesn't provide direct queue stats like BullMQ
        # This would require additional monitoring setup
        logger.info("Queue stats not directly available in Celery without additional monitoring")
        return {}

    def retry_failed_task(self, task_id: str) -> bool:
        """
        Retry a failed task.

        Args:
            task_id: Celery task ID

        Returns:
            True if retry was initiated
        """
        result = AsyncResult(task_id, app=celery_app)

        if result.state == "FAILURE":
            # In Celery, failed tasks need to be re-queued manually
            # This is more complex than BullMQ's retry mechanism
            logger.info(f"Manual retry for task {task_id} would need to be implemented")
            return False

        return False


# Create service instance
celery_service = CeleryService()
