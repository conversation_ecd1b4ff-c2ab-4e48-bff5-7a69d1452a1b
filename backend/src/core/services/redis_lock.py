"""
Redis-based locking utility for per-shop operations.
"""

import logging
import redis
from contextlib import asynccontextmanager
from typing import Optional
import asyncio
from datetime import timedelta

logger = logging.getLogger(__name__)


class RedisLock:
    """Redis-based distributed lock for preventing concurrent operations."""

    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_client = redis.from_url(redis_url)
        self.default_timeout = 300  # 5 minutes default lock timeout

    @asynccontextmanager
    async def acquire_lock(self, lock_key: str, timeout: Optional[int] = None):
        """
        Acquire a Redis lock with automatic cleanup.

        Args:
            lock_key: Unique lock key (e.g., f"lock:shop:{shop_id}")
            timeout: Lock timeout in seconds

        Yields:
            bool: True if lock acquired, False otherwise
        """
        lock_timeout = timeout or self.default_timeout
        lock_acquired = False

        try:
            # Try to acquire lock
            lock_acquired = self.redis_client.set(
                lock_key,
                "locked",
                ex=lock_timeout,
                nx=True  # Only set if key doesn't exist
            )

            if lock_acquired:
                logger.info(f"Acquired lock: {lock_key}")
                yield True
            else:
                logger.warning(f"Failed to acquire lock: {lock_key}")
                yield False

        except Exception as e:
            logger.error(f"Error with Redis lock {lock_key}: {e}")
            yield False

        finally:
            if lock_acquired:
                try:
                    self.redis_client.delete(lock_key)
                    logger.info(f"Released lock: {lock_key}")
                except Exception as e:
                    logger.error(f"Error releasing lock {lock_key}: {e}")

    async def is_locked(self, lock_key: str) -> bool:
        """Check if a lock is currently held."""
        try:
            return self.redis_client.exists(lock_key) == 1
        except Exception as e:
            logger.error(f"Error checking lock {lock_key}: {e}")
            return False

    async def extend_lock(self, lock_key: str, timeout: Optional[int] = None) -> bool:
        """Extend the timeout of an existing lock."""
        lock_timeout = timeout or self.default_timeout
        try:
            # Only extend if key exists
            return self.redis_client.expire(lock_key, lock_timeout) == 1
        except Exception as e:
            logger.error(f"Error extending lock {lock_key}: {e}")
            return False


# Global lock instance
redis_lock = RedisLock()