"""
Prometheus metrics for Shopify sync system.
"""

from prometheus_client import Counter, Histogram, Gauge

# Webhook metrics
webhook_requests_total = Counter(
    'webhook_requests_total',
    'Total number of webhook requests received',
    ['shop_domain', 'topic', 'status']
)

webhook_processing_duration = Histogram(
    'webhook_processing_duration_seconds',
    'Time spent processing webhook requests',
    ['shop_domain', 'topic']
)

webhook_hmac_failures = Counter(
    'webhook_hmac_failures_total',
    'Number of HMAC validation failures',
    ['shop_domain']
)

webhook_dedup_hits = Counter(
    'webhook_dedup_hits_total',
    'Number of duplicate webhooks detected',
    ['shop_domain', 'topic']
)

# Sync job metrics
sync_jobs_total = Counter(
    'sync_jobs_total',
    'Total number of sync jobs',
    ['store_domain', 'entity_type', 'status']
)

sync_duration = Histogram(
    'sync_duration_seconds',
    'Time spent on sync operations',
    ['store_domain', 'entity_type']
)

sync_job_failures = Counter(
    'sync_job_failures_total',
    'Number of sync job failures',
    ['store_domain', 'entity_type', 'failure_reason']
)

active_sync_jobs = Gauge(
    'active_sync_jobs',
    'Number of currently active sync jobs',
    ['store_domain']
)

# Consumer metrics
consumer_records_processed = Counter(
    'consumer_records_processed_total',
    'Total number of records processed by consumer',
    ['store_domain', 'entity_type']
)

consumer_processing_duration = Histogram(
    'consumer_processing_duration_seconds',
    'Time spent processing consumer batches',
    ['store_domain', 'entity_type']
)

staging_records_pending = Gauge(
    'staging_records_pending',
    'Number of unprocessed records in staging tables',
    ['store_domain', 'entity_type']
)

# Airbyte API metrics
airbyte_api_requests = Counter(
    'airbyte_api_requests_total',
    'Total Airbyte API requests',
    ['endpoint', 'status']
)

airbyte_api_duration = Histogram(
    'airbyte_api_duration_seconds',
    'Airbyte API request duration',
    ['endpoint']
)

# Dead letter queue metrics
dlq_items_total = Counter(
    'dlq_items_total',
    'Total items in dead letter queue',
    ['source_type', 'failure_reason']
)

dlq_items_pending = Gauge(
    'dlq_items_pending',
    'Number of unresolved items in dead letter queue',
    ['source_type']
)

# Media generation metrics
media_generation_duration = Histogram(
    'media_generation_duration_seconds',
    'Time spent on media generation operations',
    ['media_type', 'status']
)

media_generation_failures = Counter(
    'media_generation_failures_total',
    'Number of media generation failures',
    ['media_type', 'failure_reason']
)

# Media push metrics
media_push_duration = Histogram(
    'media_push_duration_seconds',
    'Time spent on media push operations',
    ['platform', 'status']
)

media_push_failures = Counter(
    'media_push_failures_total',
    'Number of media push failures',
    ['platform', 'failure_reason']
)

# Analytics processing metrics
analytics_processing_duration = Histogram(
    'analytics_processing_duration_seconds',
    'Time spent on analytics processing operations',
    []
)

analytics_processing_failures = Counter(
    'analytics_processing_failures_total',
    'Number of analytics processing failures',
    ['failure_reason']
)
