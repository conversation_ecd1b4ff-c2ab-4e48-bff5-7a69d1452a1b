# Airbyte Environment Configuration
AIRBYTE_VERSION=0.63.15

# Database Configuration (shared across Airbyte services)
# All Airbyte services now use the main db service
DATABASE_HOST=db
DATABASE_PORT=5432
DATABASE_PASSWORD=dev_password
DATABASE_USER=app_user
DATABASE_DB=airbyte
DATABASE_URL=*********************************

# Common Settings
LOG_LEVEL=INFO

# Airbyte Worker Configuration
AUTO_DETECT_SCHEMA=true
WORKSPACE_ROOT=/tmp/workspace
WORKSPACE_DOCKER_MOUNT=airbyte_workspace
LOCAL_ROOT=/tmp/airbyte_local
LOCAL_DOCKER_MOUNT=/tmp/airbyte_local
WEBAPP_URL=http://airbyte-webapp:80
TEMPORAL_HOST=airbyte-temporal:7233
WORKER_ENVIRONMENT=docker

# Storage Configuration
S3_LOG_BUCKET=
S3_LOG_BUCKET_REGION=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
S3_MINIO_ENDPOINT=http://minio:9000
S3_PATH_STYLE_ACCESS=true
GOOGLE_APPLICATION_CREDENTIALS=
GCS_LOG_BUCKET=

# Performance Settings
JOB_MAIN_CONTAINER_CPU_REQUEST=
JOB_MAIN_CONTAINER_CPU_LIMIT=
JOB_MAIN_CONTAINER_MEMORY_REQUEST=
JOB_MAIN_CONTAINER_MEMORY_LIMIT=
NORMALIZATION_JOB_MAIN_CONTAINER_MEMORY_LIMIT=
NORMALIZATION_JOB_MAIN_CONTAINER_MEMORY_REQUEST=
NORMALIZATION_JOB_MAIN_CONTAINER_CPU_LIMIT=
NORMALIZATION_JOB_MAIN_CONTAINER_CPU_REQUEST=

# Security & Features
SECRET_PERSISTENCE=TESTING_CONFIG_DB_TABLE
USE_STREAM_CAPABLE_STATE=true
MICRONAUT_ENVIRONMENTS=control-plane

# Storage Configuration
STORAGE_TYPE=LOCAL

# Local Secrets Database Configuration (required for TESTING_CONFIG_DB_TABLE)
datasources.local-secrets.url=**************************************
datasources.local-secrets.username=app_user
datasources.local-secrets.password=dev_password
datasources.local-secrets.driver-class-name=org.postgresql.Driver
datasources.local-secrets.maximum-pool-size=10
datasources.local-secrets.connection-timeout=20000

# Airbyte Server Configuration
CONFIG_ROOT=/data
TRACKING_STRATEGY=logging

# Airbyte Webapp Configuration
AIRBYTE_SERVER_HOST=airbyte-server
CONNECTOR_BUILDER_API_HOST=airbyte-server
API_URL=/api/v1/
KEYCLOAK_INTERNAL_HOST=
PAPERCUPS_STOREFRONT_TOKEN=
FULLSTORY_ORG_ID=

# Temporal Configuration
DB=postgresql
POSTGRES_USER=app_user
POSTGRES_PWD=dev_password
POSTGRES_SEEDS=db
POSTGRES_PORT=5432
DBNAME=temporal
VISIBILITY_DBNAME=temporal_visibility
DYNAMIC_CONFIG_FILE_PATH=config/dynamicconfig/development.yaml